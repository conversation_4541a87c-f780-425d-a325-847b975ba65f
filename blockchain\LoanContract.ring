# عقد القروض الذكي
# يدير عمليات الإقراض والاقتراض بين المستخدمين

load "SmartContract.ring"

class LoanContract from SmartContract {
    private {
        # حالة القرض
        aLoanStates = [ :REQUESTED = 1,
                       :APPROVED = 2,
                       :ACTIVE = 3,
                       :REPAID = 4,
                       :DEFAULTED = 5 ]

        # تفاصيل القروض
        aLoans = []
        aLoanIds = []
    }

    # طلب قرض جديد
    func requestLoan(nAmount, nDuration, nCollateral) {
        if nAmount <= 0 return false
        if nDuration <= 0 return false
        if nCollateral <= 0 return false

        cBorrower = sender()
        cLoanId = generateLoanId(cBorrower)

        aLoan = [ :id = cLoanId,
                 :borrower = cBorrower,
                 :amount = nAmount,
                 :duration = nDuration,  # بالأيام
                 :collateral = nCollateral,
                 :state = aLoanStates[:REQUESTED],
                 :requestDate = time(),
                 :approvalDate = 0,
                 :repaymentDate = 0,
                 :lender = "",
                 :interestRate = calculateInterestRate(nAmount, nCollateral) ]

        add(aLoans, aLoan)
        add(aLoanIds, cLoanId)
        return cLoanId
    }

    # الموافقة على القرض
    func approveLoan(cLoanId) {
        aLoan = getLoan(cLoanId)
        if aLoan = NULL return false
        
        if aLoan[:state] != aLoanStates[:REQUESTED] return false
        
        cLender = sender()
        if cLender = aLoan[:borrower] return false

        aLoan[:lender] = cLender
        aLoan[:state] = aLoanStates[:APPROVED]
        aLoan[:approvalDate] = time()
        updateLoan(cLoanId, aLoan)

        return true
    }

    # تنشيط القرض (تحويل الضمان)
    func activateLoan(cLoanId) {
        aLoan = getLoan(cLoanId)
        if aLoan = NULL return false
        
        if aLoan[:state] != aLoanStates[:APPROVED] return false
        if sender() != aLoan[:borrower] return false

        # في التطبيق الفعلي، نتحقق من تحويل الضمان
        aLoan[:state] = aLoanStates[:ACTIVE]
        updateLoan(cLoanId, aLoan)

        return true
    }

    # سداد القرض
    func repayLoan(cLoanId) {
        aLoan = getLoan(cLoanId)
        if aLoan = NULL return false
        
        if aLoan[:state] != aLoanStates[:ACTIVE] return false
        if sender() != aLoan[:borrower] return false

        nTotalAmount = calculateTotalRepayment(aLoan)
        # في التطبيق الفعلي، نتحقق من السداد

        aLoan[:state] = aLoanStates[:REPAID]
        aLoan[:repaymentDate] = time()
        updateLoan(cLoanId, aLoan)

        return true
    }

    # تعيين القرض كمتعثر
    func defaultLoan(cLoanId) {
        aLoan = getLoan(cLoanId)
        if aLoan = NULL return false
        
        if aLoan[:state] != aLoanStates[:ACTIVE] return false
        if not isLoanOverdue(aLoan) return false

        aLoan[:state] = aLoanStates[:DEFAULTED]
        updateLoan(cLoanId, aLoan)

        return true
    }

    # التحقق من صحة القرض
    private func isValidLoan(cLoanId) {
        return find(aLoanIds, cLoanId) != 0
    }

    # الحصول على تفاصيل القرض
    private func getLoan(cLoanId) {
        nIndex = find(aLoanIds, cLoanId)
        if nIndex = 0 return NULL
        return aLoans[nIndex]
    }

    # تحديث تفاصيل القرض
    private func updateLoan(cLoanId, aNewLoan) {
        nIndex = find(aLoanIds, cLoanId)
        if nIndex != 0 {
            aLoans[nIndex] = aNewLoan
        }
    }

    # حساب معدل الفائدة
    private func calculateInterestRate(nAmount, nCollateral) {
        # معادلة بسيطة: كلما زاد الضمان، قل معدل الفائدة
        nRatio = nCollateral / nAmount
        if nRatio >= 2 return 0.05  # 5%
        if nRatio >= 1.5 return 0.08  # 8%
        if nRatio >= 1 return 0.1  # 10%
        return 0.15  # 15%
    }

    # حساب المبلغ الإجمالي للسداد
    private func calculateTotalRepayment(aLoan) {
        nAmount = aLoan[:amount]
        nDuration = aLoan[:duration]
        nRate = aLoan[:interestRate]
        
        # معادلة بسيطة: المبلغ + (المبلغ * المعدل * المدة بالسنوات)
        return nAmount + (nAmount * nRate * (nDuration / 365))
    }

    # التحقق من تجاوز موعد السداد
    private func isLoanOverdue(aLoan) {
        if aLoan[:state] != aLoanStates[:ACTIVE] return false
        
        nStartDate = aLoan[:approvalDate]
        nDuration = aLoan[:duration] * 24 * 60 * 60  # تحويل الأيام إلى ثواني
        return (time() - nStartDate) > nDuration
    }

    # توليد معرف فريد للقرض
    private func generateLoanId(cBorrower) {
        cData = cBorrower + string(time()) + string(random(1000000))
        return "LOAN-" + substr(sha256(cData), 1, 8)
    }

    # التحقق من صحة الإجراء
    private func isValidAction(cAction) {
        switch cAction {
            case "requestLoan"
                return true
            case "approveLoan"
                return true
            case "activateLoan"
                return true
            case "repayLoan"
                return true
            case "defaultLoan"
                return true
            default
                return false
        }
    }

    # تنفيذ الإجراء
    private func executeAction(cAction, aParams) {
        switch cAction {
            case "requestLoan"
                return requestLoan(aParams[:amount], aParams[:duration], aParams[:collateral])
            case "approveLoan"
                return approveLoan(aParams[:loanId])
            case "activateLoan"
                return activateLoan(aParams[:loanId])
            case "repayLoan"
                return repayLoan(aParams[:loanId])
            case "defaultLoan"
                return defaultLoan(aParams[:loanId])
            default
                return false
        }
    }
}
