class Wallet {
    constructor(api, auth) {
        this.api = api;
        this.auth = auth;
        this.initEventListeners();
    }

    initEventListeners() {
        document.getElementById('walletLink').addEventListener('click', () => this.loadWallet());
        document.getElementById('transferForm').addEventListener('submit', (e) => this.handleTransfer(e));
    }

    async loadWallet() {
        try {
            await Promise.all([
                this.loadBalance(),
                this.loadTransactions(),
                this.loadBlockchainInfo()
            ]);
            
            this.showWalletSection();
        } catch (error) {
            this.showErrorMessage('حدث خطأ أثناء تحميل المحفظة');
        }
    }

    async loadBalance() {
        try {
            const response = await this.api.getBalance(this.auth.currentUser.id);
            
            if (response.success) {
                document.getElementById('walletBalance').textContent = response.data.balance.toFixed(2);
            } else {
                this.showErrorMessage(response.error);
            }
        } catch (error) {
            this.showErrorMessage('حدث خطأ أثناء تحميل الرصيد');
        }
    }

    async loadTransactions() {
        try {
            const response = await this.api.getTransactions(this.auth.currentUser.id);
            
            if (response.success) {
                this.renderTransactions(response.data);
            } else {
                this.showErrorMessage(response.error);
            }
        } catch (error) {
            this.showErrorMessage('حدث خطأ أثناء تحميل المعاملات');
        }
    }

    async loadBlockchainInfo() {
        try {
            const [blocksResponse, verifyResponse] = await Promise.all([
                this.api.getBlocks(),
                this.api.verifyChain()
            ]);

            if (blocksResponse.success && verifyResponse.success) {
                this.renderBlockchainInfo(blocksResponse.data, verifyResponse.data);
            } else {
                this.showErrorMessage(blocksResponse.error || verifyResponse.error);
            }
        } catch (error) {
            this.showErrorMessage('حدث خطأ أثناء تحميل معلومات البلوكتشين');
        }
    }

    renderTransactions(transactions) {
        const container = document.getElementById('transactionsContainer');
        container.innerHTML = '<h3>المعاملات السابقة</h3>';

        if (transactions.length === 0) {
            container.innerHTML += '<p>لا توجد معاملات سابقة</p>';
            return;
        }

        const list = document.createElement('div');
        list.className = 'transactions-list';

        transactions.forEach(transaction => {
            const element = document.createElement('div');
            element.className = 'transaction';
            
            const isSender = transaction.sender_id === this.auth.currentUser.id;
            const amount = transaction.amount.toFixed(2);
            const otherUser = isSender ? transaction.receiver_username : transaction.sender_username;
            const type = isSender ? 'إرسال' : 'استلام';
            const icon = isSender ? 'fa-arrow-up' : 'fa-arrow-down';
            const status = transaction.block_id ? 'مؤكدة' : 'قيد الانتظار';

            element.innerHTML = `
                <div class="transaction-icon">
                    <i class="fas ${icon}"></i>
                </div>
                <div class="transaction-details">
                    <div class="transaction-type">${type} إلى ${otherUser}</div>
                    <div class="transaction-amount">${amount} SBC</div>
                    <div class="transaction-status">${status}</div>
                    <div class="transaction-time">${this.formatDate(transaction.created_at)}</div>
                </div>
            `;

            list.appendChild(element);
        });

        container.appendChild(list);
    }

    renderBlockchainInfo(blocks, verifyData) {
        const container = document.getElementById('blockchainStats');
        container.innerHTML = `
            <div class="blockchain-stat">
                <div class="stat-label">عدد الكتل</div>
                <div class="stat-value">${blocks.length}</div>
            </div>
            <div class="blockchain-stat">
                <div class="stat-label">حالة السلسلة</div>
                <div class="stat-value ${verifyData.valid ? 'valid' : 'invalid'}">
                    ${verifyData.valid ? 'صالحة' : 'غير صالحة'}
                </div>
            </div>
            <div class="blockchain-stat">
                <div class="stat-label">آخر تحديث</div>
                <div class="stat-value">
                    ${blocks.length > 0 ? this.formatDate(blocks[0].created_at) : 'لا يوجد'}
                </div>
            </div>
        `;
    }

    async handleTransfer(event) {
        event.preventDefault();
        
        const receiverId = document.getElementById('receiverId').value;
        const amount = parseFloat(document.getElementById('amount').value);

        try {
            const response = await this.api.createTransaction(
                this.auth.currentUser.id,
                receiverId,
                amount
            );

            if (response.success) {
                this.showSuccessMessage('تم إنشاء المعاملة بنجاح');
                document.getElementById('transferForm').reset();
                
                // بدء عملية التعدين
                await this.api.mineBlock();
                
                // تحديث المحفظة
                await this.loadWallet();
            } else {
                this.showErrorMessage(response.error);
            }
        } catch (error) {
            this.showErrorMessage('حدث خطأ أثناء إنشاء المعاملة');
        }
    }

    showWalletSection() {
        document.getElementById('authSection').style.display = 'none';
        document.getElementById('feedSection').style.display = 'none';
        document.getElementById('profileSection').style.display = 'none';
        document.getElementById('walletSection').style.display = 'block';
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    showSuccessMessage(message) {
        // يمكن استخدام مكتبة للإشعارات مثل toastr
        alert(message);
    }

    showErrorMessage(message) {
        // يمكن استخدام مكتبة للإشعارات مثل toastr
        alert(message);
    }
}
