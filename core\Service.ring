# الفئة الأساسية للخدمات في التطبيق
# توفر الدوال المشتركة لمعالجة النجاح والأخطاء في العمليات
class Service {
    private {
        # متغير لتخزين رسائل الخطأ
        cError
    }
    
    # دالة معالجة النجاح: تقوم بتغليف البيانات في نسق موحد للاستجابة
    func handleSuccess(data) {
        return [
            :success = true,
            :data = data
        ]
    }
    
    # دالة معالجة الخطأ: تقوم بتغليف رسالة الخطأ في نسق موحد للاستجابة
    func handleError(cMessage) {
        return [
            :success = false,
            :error = cMessage
        ]
    }
    
    # دوال الوصول لمتغير الخطأ
    func getError() { return cError }              # استرجاع رسالة الخطأ
    func setError(error) { cError = error }        # تعيين رسالة الخطأ
}
