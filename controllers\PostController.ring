# متحكم المنشورات - يدير العمليات المتعلقة بالمنشورات والتعليقات
# يتضمن إنشاء وتحديث وحذف المنشورات والتعليقات، والتفاعلات معها

load "../models/Post.ring"
load "../models/Comment.ring"
load "../models/Notification.ring"
load "../utils/Validator.ring"
load "../utils/Logger.ring"

class PostController {
    # المتغيرات الخاصة بالمتحكم
    private {
        aPosts = []         # قائمة المنشورات
        aComments = []      # قائمة التعليقات
        oLogger = NULL      # كائن التسجيل
        cStatus = "active"  # حالة المتحكم
    }

    # دالة البناء
    func init() {
        oLogger = new Logger("posts")
    }

    # إضافة منشور جديد
    func addPost(oPost) {
        if not isValidPost(oPost) {
            oLogger.error("المنشور غير صالح")
            return false
        }

        add(aPosts, oPost)
        oLogger.info("تم إضافة منشور جديد: " + oPost.getId())

        # إرسال إشعار للمتابعين
        notifyFollowers(oPost)
        return true
    }

    # تحديث منشور
    func updatePost(cPostId, oUpdatedPost) {
        nIndex = findPostIndex(cPostId)
        if nIndex = 0 {
            oLogger.error("المنشور غير موجود: " + cPostId)
            return false
        }

        if not isValidPost(oUpdatedPost) {
            oLogger.error("بيانات التحديث غير صالحة")
            return false
        }

        aPosts[nIndex] = oUpdatedPost
        oLogger.info("تم تحديث المنشور: " + cPostId)
        return true
    }

    # حذف منشور
    func deletePost(cPostId) {
        nIndex = findPostIndex(cPostId)
        if nIndex = 0 {
            oLogger.error("المنشور غير موجود: " + cPostId)
            return false
        }

        # حذف جميع التعليقات المرتبطة بالمنشور
        deletePostComments(cPostId)

        # حذف المنشور
        del(aPosts, nIndex)
        oLogger.info("تم حذف المنشور: " + cPostId)
        return true
    }

    # إضافة تعليق
    func addComment(oComment) {
        if not isValidComment(oComment) {
            oLogger.error("التعليق غير صالح")
            return false
        }

        # التحقق من وجود المنشور
        cPostId = oComment.getPostId()
        if findPostIndex(cPostId) = 0 {
            oLogger.error("المنشور غير موجود: " + cPostId)
            return false
        }

        add(aComments, oComment)
        oLogger.info("تم إضافة تعليق جديد: " + oComment.getId())

        # إرسال إشعار لصاحب المنشور
        notifyPostOwner(oComment)
        return true
    }

    # تحديث تعليق
    func updateComment(cCommentId, oUpdatedComment) {
        nIndex = findCommentIndex(cCommentId)
        if nIndex = 0 {
            oLogger.error("التعليق غير موجود: " + cCommentId)
            return false
        }

        if not isValidComment(oUpdatedComment) {
            oLogger.error("بيانات التحديث غير صالحة")
            return false
        }

        aComments[nIndex] = oUpdatedComment
        oLogger.info("تم تحديث التعليق: " + cCommentId)
        return true
    }

    # حذف تعليق
    func deleteComment(cCommentId) {
        nIndex = findCommentIndex(cCommentId)
        if nIndex = 0 {
            oLogger.error("التعليق غير موجود: " + cCommentId)
            return false
        }

        del(aComments, nIndex)
        oLogger.info("تم حذف التعليق: " + cCommentId)
        return true
    }

    # الحصول على منشور
    func getPost(cPostId) {
        nIndex = findPostIndex(cPostId)
        if nIndex = 0 return NULL
        return aPosts[nIndex]
    }

    # الحصول على تعليقات منشور
    func getPostComments(cPostId) {
        aPostComments = []
        for oComment in aComments {
            if oComment.getPostId() = cPostId {
                add(aPostComments, oComment)
            }
        }
        return aPostComments
    }

    # الحصول على منشورات مستخدم
    func getUserPosts(cUserId) {
        aUserPosts = []
        for oPost in aPosts {
            if oPost.getUserId() = cUserId {
                add(aUserPosts, oPost)
            }
        }
        return aUserPosts
    }

    # دوال المساعدة

    # البحث عن مؤشر المنشور
    private func findPostIndex(cPostId) {
        for i = 1 to len(aPosts) {
            if aPosts[i].getId() = cPostId {
                return i
            }
        }
        return 0
    }

    # البحث عن مؤشر التعليق
    private func findCommentIndex(cCommentId) {
        for i = 1 to len(aComments) {
            if aComments[i].getId() = cCommentId {
                return i
            }
        }
        return 0
    }

    # حذف تعليقات منشور
    private func deletePostComments(cPostId) {
        i = 1
        while i <= len(aComments) {
            if aComments[i].getPostId() = cPostId {
                del(aComments, i)
            } else {
                i++
            }
        }
    }

    # التحقق من صحة منشور
    private func isValidPost(oPost) {
        if not isobject(oPost) return false
        if not oPost.isValid() return false
        return true
    }

    # التحقق من صحة تعليق
    private func isValidComment(oComment) {
        if not isobject(oComment) return false
        if not oComment.isValid() return false
        return true
    }

    # إرسال إشعار للمتابعين
    private func notifyFollowers(oPost) {
        aFollowers = oPost.getUser().getFollowers()
        for cFollowerId in aFollowers {
            oNotification = new Notification(
                cFollowerId,
                "post",
                oPost.getUser().getName() + " قام بنشر منشور جديد"
            )
            oNotification.setSource(oPost.getId(), "post")
            addNotification(oNotification)
        }
    }

    # إرسال إشعار لصاحب المنشور
    private func notifyPostOwner(oComment) {
        oPost = getPost(oComment.getPostId())
        if oPost = NULL return

        # تجنب إرسال إشعار إذا كان المعلق هو نفسه صاحب المنشور
        if oComment.getUserId() = oPost.getUserId() return

        oNotification = new Notification(
            oPost.getUserId(),
            "comment",
            oComment.getUser().getName() + " علق على منشورك"
        )
        oNotification.setSource(oComment.getId(), "comment")
        addNotification(oNotification)
    }

    # تحويل المتحكم إلى قائمة
    func toList() {
        aData = []
        aData[:posts] = []
        for oPost in aPosts {
            add(aData[:posts], oPost.toList())
        }
        aData[:comments] = []
        for oComment in aComments {
            add(aData[:comments], oComment.toList())
        }
        aData[:status] = cStatus
        return aData
    }

    # تحميل المتحكم من قائمة
    func fromList(aData) {
        aPosts = []
        for aPostData in aData[:posts] {
            oPost = new Post()
            oPost.fromList(aPostData)
            add(aPosts, oPost)
        }
        aComments = []
        for aCommentData in aData[:comments] {
            oComment = new Comment()
            oComment.fromList(aCommentData)
            add(aComments, oComment)
        }
        cStatus = aData[:status]
    }
}
