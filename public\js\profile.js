class Profile {
    constructor(api, auth) {
        this.api = api;
        this.auth = auth;
        this.initEventListeners();
    }

    initEventListeners() {
        document.getElementById('profileLink').addEventListener('click', () => this.loadProfile());
        document.getElementById('editProfileBtn').addEventListener('click', () => this.showEditProfileModal());
        document.getElementById('editProfileForm').addEventListener('submit', (e) => this.handleUpdateProfile(e));
        document.getElementById('profileAvatar').addEventListener('click', () => document.getElementById('avatarInput').click());
        document.getElementById('avatarInput').addEventListener('change', (e) => this.handleAvatarUpload(e));
    }

    async loadProfile() {
        try {
            const response = await this.api.getProfile(this.auth.currentUser.id);
            
            if (response.success) {
                this.renderProfile(response.data);
                this.showProfileSection();
            } else {
                this.showErrorMessage(response.error);
            }
        } catch (error) {
            this.showErrorMessage('حدث خطأ أثناء تحميل الملف الشخصي');
        }
    }

    renderProfile(profile) {
        document.getElementById('profileUsername').textContent = profile.username;
        document.getElementById('profileBio').textContent = profile.bio || 'لا توجد نبذة شخصية';
        document.getElementById('profileAvatar').src = profile.avatar || 'images/default-avatar.png';
        document.getElementById('postsCount').textContent = profile.posts_count;
        document.getElementById('followersCount').textContent = profile.followers_count;
        document.getElementById('followingCount').textContent = profile.following_count;
        
        this.loadUserPosts();
    }

    async loadUserPosts() {
        try {
            const response = await this.api.getFeed(this.auth.currentUser.id);
            
            if (response.success) {
                this.renderUserPosts(response.data);
            } else {
                this.showErrorMessage(response.error);
            }
        } catch (error) {
            this.showErrorMessage('حدث خطأ أثناء تحميل المنشورات');
        }
    }

    renderUserPosts(posts) {
        const container = document.getElementById('profilePosts');
        container.innerHTML = '';

        posts.forEach(post => {
            container.appendChild(this.createPostElement(post));
        });
    }

    createPostElement(post) {
        const element = document.createElement('div');
        element.className = 'post';
        element.innerHTML = `
            <div class="post-content">${post.content}</div>
            ${post.media_url ? `<img src="${post.media_url}" class="post-media" alt="صورة المنشور">` : ''}
            <div class="post-stats">
                <span>${post.likes_count} إعجاب</span>
                <span>${post.comments_count} تعليق</span>
                <span>${post.shares_count} مشاركة</span>
            </div>
        `;
        return element;
    }

    showEditProfileModal() {
        const modal = document.getElementById('editProfileModal');
        const bio = document.getElementById('profileBio').textContent;
        document.getElementById('editBio').value = bio === 'لا توجد نبذة شخصية' ? '' : bio;
        modal.style.display = 'block';

        // إغلاق النافذة المنبثقة عند النقر خارجها
        modal.onclick = (event) => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        };

        document.querySelector('.close').onclick = () => {
            modal.style.display = 'none';
        };
    }

    async handleUpdateProfile(event) {
        event.preventDefault();
        
        const bio = document.getElementById('editBio').value;
        const avatar = document.getElementById('profileAvatar').src;

        try {
            const response = await this.api.updateProfile(
                this.auth.currentUser.id,
                bio,
                avatar
            );

            if (response.success) {
                document.getElementById('editProfileModal').style.display = 'none';
                this.showSuccessMessage('تم تحديث الملف الشخصي بنجاح');
                await this.loadProfile();
            } else {
                this.showErrorMessage(response.error);
            }
        } catch (error) {
            this.showErrorMessage('حدث خطأ أثناء تحديث الملف الشخصي');
        }
    }

    async handleAvatarUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // هنا يمكن إضافة رفع الملف إلى خدمة تخزين
        // وتحديث صورة الملف الشخصي
        // مثال:
        // const url = await uploadFile(file);
        // document.getElementById('profileAvatar').src = url;
    }

    showProfileSection() {
        document.getElementById('authSection').style.display = 'none';
        document.getElementById('feedSection').style.display = 'none';
        document.getElementById('profileSection').style.display = 'block';
        document.getElementById('walletSection').style.display = 'none';
    }

    showSuccessMessage(message) {
        // يمكن استخدام مكتبة للإشعارات مثل toastr
        alert(message);
    }

    showErrorMessage(message) {
        // يمكن استخدام مكتبة للإشعارات مثل toastr
        alert(message);
    }
}
