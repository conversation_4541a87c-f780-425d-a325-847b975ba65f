# أداة التشفير - تدير عمليات التشفير وفك التشفير والتوقيع الرقمي
# تتضمن دعم RSA و AES وإدارة المفاتيح

load "openssl.ring"

class Crypto {
    # المتغيرات الخاصة
    private {
        nRsaKeySize = 2048      # حجم مفتاح RSA
        nAesKeySize = 256       # حجم مفتاح AES
        cAesMode = "CBC"        # نمط التشفير AES
        oLogger = NULL          # كائن التسجيل
    }

    # دالة البناء
    func init() {
        oLogger = new Logger("crypto")
    }

    # توليد زوج مفاتيح RSA
    func generateKeyPair() {
        try {
            oRsa = ssl_rsa_new()
            ssl_rsa_generate_key(oRsa, nRsaKeySize)
            
            aKeyPair = [
                :public = ssl_rsa_get_public_key(oRsa),
                :private = ssl_rsa_get_private_key(oRsa)
            ]
            
            ssl_rsa_free(oRsa)
            oLogger.info("تم توليد زوج مفاتيح RSA جديد")
            return aKeyPair
        catch
            oLogger.error("فشل في توليد زوج المفاتيح: " + cCatchError)
            return NULL
        }
    }

    # تأمين المفتاح الخاص باستخدام كلمة المرور
    func securePrivateKey(cPrivateKey, cPassword) {
        try {
            # توليد مفتاح AES من كلمة المرور
            cSalt = generateRandomBytes(16)
            cAesKey = deriveKey(cPassword, cSalt)
            
            # توليد متجه التهيئة
            cIv = generateRandomBytes(16)
            
            # تشفير المفتاح الخاص
            cEncryptedKey = aesEncrypt(cPrivateKey, cAesKey, cIv)
            
            if cEncryptedKey = NULL {
                return NULL
            }
            
            oLogger.info("تم تأمين المفتاح الخاص")
            return [
                :key = cEncryptedKey,
                :iv = cIv,
                :salt = cSalt
            ]
        catch
            oLogger.error("فشل في تأمين المفتاح الخاص: " + cCatchError)
            return NULL
        }
    }

    # استرجاع المفتاح الخاص باستخدام كلمة المرور
    func retrievePrivateKey(cEncryptedKey, cIv, cSalt, cPassword) {
        try {
            # اشتقاق مفتاح AES من كلمة المرور
            cAesKey = deriveKey(cPassword, cSalt)
            
            # فك تشفير المفتاح الخاص
            cPrivateKey = aesDecrypt(cEncryptedKey, cAesKey, cIv)
            
            if cPrivateKey = NULL {
                return NULL
            }
            
            oLogger.info("تم استرجاع المفتاح الخاص")
            return cPrivateKey
       catch
            oLogger.error("فشل في استرجاع المفتاح الخاص: " + cCatchError)
            return NULL
        }
    }

    # التوقيع على البيانات
    func sign(cData, cPrivateKey) {
        try {
            oRsa = ssl_rsa_new()
            ssl_rsa_set_private_key(oRsa, cPrivateKey)
            
            cSignature = ssl_rsa_sign(oRsa, cData)
            ssl_rsa_free(oRsa)
            
            if cSignature = NULL {
                oLogger.error("فشل في التوقيع على البيانات")
                return NULL
            }
            
            oLogger.info("تم التوقيع على البيانات")
            return cSignature
       catch
            oLogger.error("فشل في التوقيع: " + cCatchError)
            return NULL
        }
    }

    # التحقق من التوقيع
    func verify(cData, cSignature, cPublicKey) {
        try {
            oRsa = ssl_rsa_new()
            ssl_rsa_set_public_key(oRsa, cPublicKey)
            
            bValid = ssl_rsa_verify(oRsa, cData, cSignature)
            ssl_rsa_free(oRsa)
            
            if bValid {
                oLogger.info("تم التحقق من صحة التوقيع")
            } else {
                oLogger.warning("التوقيع غير صالح")
            }
            
            return bValid
       catch
            oLogger.error("فشل في التحقق من التوقيع: " + cCatchError)
            return false
        }
    }

    # تشفير البيانات باستخدام RSA
    func rsaEncrypt(cData, cPublicKey) {
        try {
            oRsa = ssl_rsa_new()
            ssl_rsa_set_public_key(oRsa, cPublicKey)
            
            cEncrypted = ssl_rsa_encrypt(oRsa, cData)
            ssl_rsa_free(oRsa)
            
            if cEncrypted = NULL {
                oLogger.error("فشل في تشفير البيانات باستخدام RSA")
                return NULL
            }
            
            oLogger.info("تم تشفير البيانات باستخدام RSA")
            return cEncrypted
       catch
            oLogger.error("فشل في التشفير RSA: " + cCatchError)
            return NULL
        }
    }

    # فك تشفير البيانات باستخدام RSA
    func rsaDecrypt(cEncrypted, cPrivateKey) {
        try {
            oRsa = ssl_rsa_new()
            ssl_rsa_set_private_key(oRsa, cPrivateKey)
            
            cDecrypted = ssl_rsa_decrypt(oRsa, cEncrypted)
            ssl_rsa_free(oRsa)
            
            if cDecrypted = NULL {
                oLogger.error("فشل في فك تشفير البيانات باستخدام RSA")
                return NULL
            }
            
            oLogger.info("تم فك تشفير البيانات باستخدام RSA")
            return cDecrypted
       catch
            oLogger.error("فشل في فك التشفير RSA: " + cCatchError)
            return NULL
        }
    }

    # تشفير البيانات باستخدام AES
    private func aesEncrypt(cData, cKey, cIv) {
        try {
            oAes = ssl_aes_new()
            ssl_aes_set_key(oAes, cKey)
            ssl_aes_set_iv(oAes, cIv)
            ssl_aes_set_mode(oAes, cAesMode)
            
            cEncrypted = ssl_aes_encrypt(oAes, cData)
            ssl_aes_free(oAes)
            
            return cEncrypted
       catch
            return NULL
        }
    }

    # فك تشفير البيانات باستخدام AES
    private func aesDecrypt(cEncrypted, cKey, cIv) {
        try {
            oAes = ssl_aes_new()
            ssl_aes_set_key(oAes, cKey)
            ssl_aes_set_iv(oAes, cIv)
            ssl_aes_set_mode(oAes, cAesMode)
            
            cDecrypted = ssl_aes_decrypt(oAes, cEncrypted)
            ssl_aes_free(oAes)
            
            return cDecrypted
       catch
            return NULL
        }
    }

    # اشتقاق مفتاح من كلمة المرور
    private func deriveKey(cPassword, cSalt) {
        try {
            return ssl_pbkdf2(cPassword, cSalt, 10000, nAesKeySize / 8)
       catch
            return NULL
        }
    }

    # توليد بايتات عشوائية
    private func generateRandomBytes(nSize) {
        try {
            return ssl_random_bytes(nSize)
       catch
            return NULL
        }
    }

    # تعيين حجم مفتاح RSA
    func setRsaKeySize(nSize) {
        if nSize >= 2048 {
            nRsaKeySize = nSize
        }
    }

    # تعيين حجم مفتاح AES
    func setAesKeySize(nSize) {
        if nSize in [128, 192, 256] {
            nAesKeySize = nSize
        }
    }

    # تعيين نمط تشفير AES
    func setAesMode(cMode) {
        if cMode in ["CBC", "ECB", "CFB", "OFB"] {
            cAesMode = cMode
        }
    }
}
