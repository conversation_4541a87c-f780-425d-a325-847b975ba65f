<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Social Bank - منصة التواصل الاجتماعي اللامركزية</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar">
        <div class="nav-brand">Social Bank</div>
        <div class="nav-menu" id="navMenu">
            <a href="#" class="active" id="feedLink">الرئيسية</a>
            <a href="#" id="profileLink">الملف الشخصي</a>
            <a href="#" id="walletLink">المحفظة</a>
            <a href="#" id="logoutBtn" style="display: none;">تسجيل الخروج</a>
            <a href="#" id="loginBtn">تسجيل الدخول</a>
        </div>
    </nav>

    <!-- قسم تسجيل الدخول -->
    <div class="auth-container" id="authSection">
        <div class="auth-box">
            <h2>تسجيل الدخول</h2>
            <form id="loginForm">
                <input type="text" placeholder="اسم المستخدم" id="loginUsername" required>
                <input type="password" placeholder="كلمة المرور" id="loginPassword" required>
                <button type="submit">دخول</button>
            </form>
            <p>ليس لديك حساب؟ <a href="#" id="showRegister">سجل الآن</a></p>
        </div>

        <div class="auth-box" id="registerBox" style="display: none;">
            <h2>إنشاء حساب جديد</h2>
            <form id="registerForm">
                <input type="text" placeholder="اسم المستخدم" id="registerUsername" required>
                <input type="password" placeholder="كلمة المرور" id="registerPassword" required>
                <input type="password" placeholder="تأكيد كلمة المرور" id="confirmPassword" required>
                <button type="submit">تسجيل</button>
            </form>
            <p>لديك حساب بالفعل؟ <a href="#" id="showLogin">سجل دخول</a></p>
        </div>
    </div>

    <!-- قسم المنشورات -->
    <div class="feed-container" id="feedSection" style="display: none;">
        <div class="create-post">
            <form id="postForm">
                <textarea placeholder="ماذا يدور في ذهنك؟" id="postContent" required></textarea>
                <div class="post-actions">
                    <input type="file" id="postMedia" accept="image/*,video/*">
                    <button type="submit">نشر</button>
                </div>
            </form>
        </div>

        <div class="posts" id="postsContainer">
            <!-- سيتم إضافة المنشورات هنا ديناميكياً -->
        </div>
    </div>

    <!-- قسم الملف الشخصي -->
    <div class="profile-container" id="profileSection" style="display: none;">
        <div class="profile-header">
            <div class="profile-avatar">
                <img src="images/default-avatar.png" id="profileAvatar" alt="الصورة الشخصية">
                <input type="file" id="avatarInput" accept="image/*" style="display: none;">
            </div>
            <div class="profile-info">
                <h2 id="profileUsername">اسم المستخدم</h2>
                <p id="profileBio">نبذة شخصية</p>
                <button id="editProfileBtn">تعديل الملف الشخصي</button>
            </div>
            <div class="profile-stats">
                <div class="stat">
                    <span id="postsCount">0</span>
                    <span>منشورات</span>
                </div>
                <div class="stat">
                    <span id="followersCount">0</span>
                    <span>متابعون</span>
                </div>
                <div class="stat">
                    <span id="followingCount">0</span>
                    <span>يتابع</span>
                </div>
            </div>
        </div>

        <div class="profile-posts" id="profilePosts">
            <!-- سيتم إضافة منشورات المستخدم هنا -->
        </div>
    </div>

    <!-- قسم المحفظة -->
    <div class="wallet-container" id="walletSection" style="display: none;">
        <div class="wallet-header">
            <h2>محفظتك</h2>
            <div class="wallet-balance">
                <span>الرصيد:</span>
                <span id="walletBalance">0</span>
                <span>SBC</span>
            </div>
        </div>

        <div class="wallet-actions">
            <form id="transferForm">
                <input type="text" placeholder="معرف المستلم" id="receiverId" required>
                <input type="number" placeholder="المبلغ" id="amount" required min="0" step="0.01">
                <button type="submit">تحويل</button>
            </form>
        </div>

        <div class="transactions" id="transactionsContainer">
            <h3>المعاملات السابقة</h3>
            <!-- سيتم إضافة المعاملات هنا -->
        </div>

        <div class="blockchain-info">
            <h3>معلومات البلوكتشين</h3>
            <div id="blockchainStats">
                <!-- سيتم إضافة إحصائيات البلوكتشين هنا -->
            </div>
        </div>
    </div>

    <!-- النوافذ المنبثقة -->
    <div class="modal" id="editProfileModal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>تعديل الملف الشخصي</h2>
            <form id="editProfileForm">
                <textarea placeholder="نبذة شخصية" id="editBio"></textarea>
                <button type="submit">حفظ التغييرات</button>
            </form>
        </div>
    </div>

    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/posts.js"></script>
    <script src="js/profile.js"></script>
    <script src="js/wallet.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
