# مدير الإحصائيات - يدير حساب وتحليل الإحصائيات المختلفة للنظام
# يدعم إحصائيات المستخدمين والمعاملات والمنشورات والنشاط

load "../utils/Logger.ring"

class StatisticsManager {
    # المتغيرات الخاصة
    private {
        oLogger = NULL      # كائن التسجيل
        aStats = []        # مخزن الإحصائيات
    }

    # دالة البناء
    func init() {
        oLogger = new Logger("statistics")
    }

    # حساب إحصائيات المستخدم
    func calculateUserStats(oUser) {
        try {
            aUserStats = []
            
            # إحصائيات المتابعة
            aUserStats[:followers_count] = len(oUser.getFollowers())
            aUserStats[:following_count] = len(oUser.getFollowing())
            
            # إحصائيات المنشورات
            aPosts = oUser.getPosts()
            aUserStats[:posts_count] = len(aPosts)
            nTotalLikes = 0
            nTotalComments = 0
            for oPost in aPosts {
                nTotalLikes += len(oPost.getLikes())
                nTotalComments += len(oPost.getComments())
            }
            aUserStats[:total_likes] = nTotalLikes
            aUserStats[:total_comments] = nTotalComments
            
            # إحصائيات المعاملات
            aTransactions = oUser.getTransactions()
            aUserStats[:transactions_count] = len(aTransactions)
            nSentAmount = 0
            nReceivedAmount = 0
            for oTransaction in aTransactions {
                if oTransaction.getSender() = oUser.getId() {
                    nSentAmount += oTransaction.getAmount()
                }
                if oTransaction.getReceiver() = oUser.getId() {
                    nReceivedAmount += oTransaction.getAmount()
                }
            }
            aUserStats[:sent_amount] = nSentAmount
            aUserStats[:received_amount] = nReceivedAmount
            aUserStats[:net_balance] = nReceivedAmount - nSentAmount
            
            # حساب معدلات النشاط
            aUserStats[:posts_per_day] = calculatePostRate(aPosts)
            aUserStats[:transactions_per_day] = calculateTransactionRate(aTransactions)
            
            # تخزين الإحصائيات
            aStats[:user + oUser.getId()] = aUserStats
            
            oLogger.info("تم حساب إحصائيات المستخدم: " + oUser.getId())
            return aUserStats
        catch
            oLogger.error("خطأ في حساب إحصائيات المستخدم: " + cCatchError)
            return NULL
        }
    }

    # حساب إحصائيات المعاملات
    func calculateTransactionStats(aTransactions) {
        try {
            aTransactionStats = []
            
            # إحصائيات عامة
            aTransactionStats[:total_count] = len(aTransactions)
            nTotalAmount = 0
            nMaxAmount = 0
            nMinAmount = 999999999
            nTotalProcessingTime = 0
            
            for oTransaction in aTransactions {
                nAmount = oTransaction.getAmount()
                nTotalAmount += nAmount
                if nAmount > nMaxAmount { nMaxAmount = nAmount }
                if nAmount < nMinAmount { nMinAmount = nAmount }
                nTotalProcessingTime += oTransaction.getProcessingTime()
            }
            
            aTransactionStats[:total_amount] = nTotalAmount
            aTransactionStats[:average_amount] = nTotalAmount / max(len(aTransactions), 1)
            aTransactionStats[:max_amount] = nMaxAmount
            aTransactionStats[:min_amount] = nMinAmount
            aTransactionStats[:avg_processing_time] = nTotalProcessingTime / max(len(aTransactions), 1)
            
            # حساب معدل المعاملات
            aTransactionStats[:transactions_per_day] = calculateTransactionRate(aTransactions)
            
            # تخزين الإحصائيات
            aStats[:transactions] = aTransactionStats
            
            oLogger.info("تم حساب إحصائيات المعاملات")
            return aTransactionStats
        catch
            oLogger.error("خطأ في حساب إحصائيات المعاملات: " + cCatchError)
            return NULL
        }
    }

    # حساب إحصائيات المنشورات
    func calculatePostStats(aPosts) {
        try {
            aPostStats = []
            
            # إحصائيات عامة
            aPostStats[:total_count] = len(aPosts)
            nTotalLikes = 0
            nTotalComments = 0
            nMaxLikes = 0
            nMaxComments = 0
            
            for oPost in aPosts {
                nLikes = len(oPost.getLikes())
                nComments = len(oPost.getComments())
                nTotalLikes += nLikes
                nTotalComments += nComments
                if nLikes > nMaxLikes { nMaxLikes = nLikes }
                if nComments > nMaxComments { nMaxComments = nComments }
            }
            
            aPostStats[:total_likes] = nTotalLikes
            aPostStats[:total_comments] = nTotalComments
            aPostStats[:avg_likes] = nTotalLikes / max(len(aPosts), 1)
            aPostStats[:avg_comments] = nTotalComments / max(len(aPosts), 1)
            aPostStats[:max_likes] = nMaxLikes
            aPostStats[:max_comments] = nMaxComments
            
            # حساب معدل المنشورات
            aPostStats[:posts_per_day] = calculatePostRate(aPosts)
            
            # تخزين الإحصائيات
            aStats[:posts] = aPostStats
            
            oLogger.info("تم حساب إحصائيات المنشورات")
            return aPostStats
        catch
            oLogger.error("خطأ في حساب إحصائيات المنشورات: " + cCatchError)
            return NULL
        }
    }

    # حساب إحصائيات المنصة
    func calculatePlatformStats() {
        try {
            aPlatformStats = []
            
            # إحصائيات المستخدمين
            aUsers = getAllUsers()
            aPlatformStats[:total_users] = len(aUsers)
            nActiveUsers = 0
            for oUser in aUsers {
                if oUser.isActive() { nActiveUsers++ }
            }
            aPlatformStats[:active_users] = nActiveUsers
            
            # إحصائيات المنشورات
            aPosts = getAllPosts()
            aPlatformStats[:total_posts] = len(aPosts)
            nTotalLikes = 0
            nTotalComments = 0
            for oPost in aPosts {
                nTotalLikes += len(oPost.getLikes())
                nTotalComments += len(oPost.getComments())
            }
            aPlatformStats[:total_likes] = nTotalLikes
            aPlatformStats[:total_comments] = nTotalComments
            
            # إحصائيات البلوكتشين
            aBlocks = getAllBlocks()
            aTransactions = getAllTransactions()
            aPlatformStats[:total_blocks] = len(aBlocks)
            aPlatformStats[:total_transactions] = len(aTransactions)
            nTotalAmount = 0
            for oTransaction in aTransactions {
                nTotalAmount += oTransaction.getAmount()
            }
            aPlatformStats[:total_amount] = nTotalAmount
            
            # حساب معدلات النمو
            aPlatformStats[:user_growth_rate] = calculateUserGrowthRate()
            aPlatformStats[:transaction_growth_rate] = calculateTransactionGrowthRate()
            aPlatformStats[:post_growth_rate] = calculatePostGrowthRate()
            
            # تخزين الإحصائيات
            aStats[:platform] = aPlatformStats
            
            oLogger.info("تم حساب إحصائيات المنصة")
            return aPlatformStats
        catch
            oLogger.error("خطأ في حساب إحصائيات المنصة: " + cCatchError)
            return NULL
        }
    }

    # دوال مساعدة

    # حساب معدل المنشورات
    private func calculatePostRate(aPosts) {
        if len(aPosts) = 0 return 0
        
        cFirstDate = aPosts[1].getDate()
        cLastDate = aPosts[len(aPosts)].getDate()
        nDays = diffDays(cFirstDate, cLastDate)
        
        return len(aPosts) / max(nDays, 1)
    }

    # حساب معدل المعاملات
    private func calculateTransactionRate(aTransactions) {
        if len(aTransactions) = 0 return 0
        
        cFirstDate = aTransactions[1].getDate()
        cLastDate = aTransactions[len(aTransactions)].getDate()
        nDays = diffDays(cFirstDate, cLastDate)
        
        return len(aTransactions) / max(nDays, 1)
    }

    # حساب معدل نمو المستخدمين
    private func calculateUserGrowthRate() {
        aUsers = getAllUsers()
        if len(aUsers) = 0 return 0
        
        # فرز المستخدمين حسب تاريخ التسجيل
        sort(aUsers, func(x, y) {
            return x.getRegistrationDate() < y.getRegistrationDate()
        })
        
        cFirstDate = aUsers[1].getRegistrationDate()
        cLastDate = aUsers[len(aUsers)].getRegistrationDate()
        nDays = diffDays(cFirstDate, cLastDate)
        
        return len(aUsers) / max(nDays, 1)
    }

    # حساب معدل نمو المعاملات
    private func calculateTransactionGrowthRate() {
        aTransactions = getAllTransactions()
        if len(aTransactions) = 0 return 0
        
        # فرز المعاملات حسب التاريخ
        sort(aTransactions, func(x, y) {
            return x.getDate() < y.getDate()
        })
        
        cFirstDate = aTransactions[1].getDate()
        cLastDate = aTransactions[len(aTransactions)].getDate()
        nDays = diffDays(cFirstDate, cLastDate)
        
        return len(aTransactions) / max(nDays, 1)
    }

    # حساب معدل نمو المنشورات
    private func calculatePostGrowthRate() {
        aPosts = getAllPosts()
        if len(aPosts) = 0 return 0
        
        # فرز المنشورات حسب التاريخ
        sort(aPosts, func(x, y) {
            return x.getDate() < y.getDate()
        })
        
        cFirstDate = aPosts[1].getDate()
        cLastDate = aPosts[len(aPosts)].getDate()
        nDays = diffDays(cFirstDate, cLastDate)
        
        return len(aPosts) / max(nDays, 1)
    }

    # حساب الفرق بين تاريخين بالأيام
    private func diffDays(cDate1, cDate2) {
        try {
            aDate1 = split(cDate1, "/")
            aDate2 = split(cDate2, "/")
            
            oDate1 = new DateTime {
                year = number(aDate1[1])
                month = number(aDate1[2])
                day = number(aDate1[3])
            }
            
            oDate2 = new DateTime {
                year = number(aDate2[1])
                month = number(aDate2[2])
                day = number(aDate2[3])
            }
            
            return abs(oDate1.diffDays(oDate2))
        catch
            return 0
        }
    }

    # الحصول على الإحصائيات المخزنة
    func getStats(cKey) {
        return aStats[cKey]
    }

    # تحويل الإحصائيات إلى قائمة
    func toList() {
        return aStats
    }

    # تحميل الإحصائيات من قائمة
    func fromList(aData) {
        aStats = aData
    }
}
