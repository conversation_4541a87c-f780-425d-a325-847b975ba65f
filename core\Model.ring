# الفئة الأساسية للنماذج في التطبيق
# تحتوي على الخصائص والدوال المشتركة بين جميع النماذج
class Model {
    private {
        # معرف فريد للنموذج
        cId
        # تاريخ ووقت إنشاء النموذج
        dCreatedAt
        # رسالة الخطأ إن وجدت
        cError
    }
    
    # دالة التهيئة: تقوم بتوليد معرف فريد وتعيين تاريخ الإنشاء
    func init() {
        cId = random(999999999)
        dCreatedAt = date() + " " + time()
    }
    
    # دوال الوصول للخصائص الأساسية
    func getId() { return cId }                    # استرجاع المعرف
    func getCreatedAt() { return dCreatedAt }      # استرجاع تاريخ الإنشاء
    func getError() { return cError }              # استرجاع رسالة الخطأ
    func setError(error) { cError = error }        # تعيين رسالة الخطأ
    
    # تحويل النموذج إلى خريطة من البيانات
    func toMap() {
        return [
            :id = cId,
            :createdAt = dCreatedAt
        ]
    }
    
    # تعبئة بيانات النموذج من خريطة
    func fromMap(aMap) {
        cId = aMap[:id]
        dCreatedAt = aMap[:createdAt]
    }
}
