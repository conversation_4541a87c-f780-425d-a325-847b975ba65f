# استيراد المكتبات والمتحكمات اللازمة
load "weblib.ring"                               # مكتبة الويب للخادم
load "../controllers/UserController.ring"        # متحكم المستخدمين
load "../controllers/PostController.ring"        # متحكم المنشورات
load "../controllers/BlockchainController.ring"  # متحكم سلسلة الكتل

# تعريف صنف الخادم الرئيسي
class Server {
    # المتغيرات الخاصة بالخادم
    private {
        aControllers    # مصفوفة تحتوي على جميع المتحكمات
    }
    
    # دالة التهيئة: إعداد الخادم والمتحكمات
    func init() {
        # إنشاء المتحكمات وتخزينها في المصفوفة
        aControllers = [
            new UserController(self),            # متحكم المستخدمين
            new PostController(self),            # متحكم المنشورات
            new BlockchainController(self)       # متحكم سلسلة الكتل
        ]
        
        # تسجيل المسارات لكل متحكم
        for controller in aControllers {
            controller.registerRoutes()
        }
    }
    
    # دالة إضافة مسار جديد للخادم
    func addRoute(method, path, callback) {
        WebLib.addRoute(method, path, callback)
    }
    
    # دالة تشغيل الخادم على المنفذ 8080
    func start() {
        WebLib.start(8080)
    }
}
