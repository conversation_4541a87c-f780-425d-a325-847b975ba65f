# متحكم البلوكتشين - يدير العمليات المتعلقة بسلسلة الكتل
# يتضمن إنشاء الكتل، التحقق من صحتها، وإدارة المعاملات

load "../models/Block.ring"
load "../models/Transaction.ring"
load "../models/Notification.ring"
load "../utils/Validator.ring"
load "../utils/Logger.ring"
load "../utils/Cache.ring"

class BlockchainController {
    # المتغيرات الخاصة بالمتحكم
    private {
        aBlocks = []             # سلسلة الكتل
        aTransactions = []       # قائمة المعاملات المعلقة
        nDifficulty = 4         # صعوبة التعدين
        cLastHash = "0"         # آخر هاش
        oLogger = NULL          # كائن التسجيل
        cStatus = "active"      # حالة المتحكم
        oCache = NULL          # كائن التخزين المؤقت
        nMaxTransactionsPerBlock = 10  # الحد الأقصى للمعاملات في الكتلة
        nMinTransactionsForMining = 5  # الحد الأدنى للمعاملات لبدء التعدين
    }

    # دالة البناء
    func init() {
        oLogger = new Logger("blockchain")
        oCache = new Cache()
        oCache.setMaxSize(10000)  # تعيين حجم التخزين المؤقت
        # إنشاء كتلة البداية
        addGenesisBlock()
    }

    # إضافة كتلة جديدة
    func addBlock(aTransactionsList) {
        if not isValidTransactionsList(aTransactionsList) {
            oLogger.error("قائمة المعاملات غير صالحة")
            return false
        }

        oBlock = new Block()
        oBlock.setPreviousHash(getLastBlock().getHash())
        oBlock.setTransactions(aTransactionsList)
        oBlock.setDifficulty(nDifficulty)
        oBlock.mine()

        if not isValidBlock(oBlock) {
            oLogger.error("فشل التحقق من صحة الكتلة")
            return false
        }

        add(aBlocks, oBlock)
        cLastHash = oBlock.getHash()
        
        # إرسال إشعارات للمستخدمين المعنيين
        notifyUsers(oBlock)
        
        oLogger.info("تمت إضافة كتلة جديدة بنجاح: " + oBlock.getHash())
        return true
    }

    # إضافة معاملة جديدة للقائمة المعلقة
    func addTransaction(oTransaction) {
        if not isValidTransaction(oTransaction) {
            oLogger.error("المعاملة غير صالحة")
            return false
        }

        # تخزين المعاملة في الذاكرة المؤقتة
        cTransactionKey = "transaction_" + oTransaction.getId()
        oCache.set(cTransactionKey, oTransaction, 7200)  # تخزين لمدة ساعتين

        add(aTransactions, oTransaction)
        oLogger.info("تمت إضافة معاملة جديدة للقائمة المعلقة: " + oTransaction.getId())

        # التحقق من إمكانية بدء التعدين
        if len(aTransactions) >= nMinTransactionsForMining {
            mineBlock()
        }

        return true
    }

    # إنشاء كتلة جديدة من المعاملات المعلقة
    func mineBlock() {
        if len(aTransactions) < nMinTransactionsForMining {
            oLogger.info("عدد المعاملات المعلقة أقل من الحد الأدنى المطلوب للتعدين")
            return false
        }

        # إنشاء قائمة المعاملات للكتلة الجديدة
        aTransactionsList = []
        
        # اختيار المعاملات ذات الأولوية العالية
        aHighPriorityTransactions = getPriorityTransactions()
        
        # إضافة المعاملات ذات الأولوية أولاً
        for oTransaction in aHighPriorityTransactions {
            if len(aTransactionsList) >= nMaxTransactionsPerBlock {
                exit
            }
            add(aTransactionsList, oTransaction)
        }

        # إضافة المعاملات العادية إذا كان هناك مساحة
        nRemainingSlots = nMaxTransactionsPerBlock - len(aTransactionsList)
        if nRemainingSlots > 0 {
            for i = 1 to min(nRemainingSlots, len(aTransactions)) {
                if not inList(aTransactionsList, aTransactions[i]) {
                    add(aTransactionsList, aTransactions[i])
                }
            }
        }

        # حذف المعاملات المستخدمة من القائمة المعلقة
        for oTransaction in aTransactionsList {
            # حذف من الذاكرة المؤقتة
            oCache.remove("transaction_" + oTransaction.getId())
            # حذف من قائمة المعاملات المعلقة
            del(aTransactions, find(aTransactions, oTransaction))
        }

        # إضافة الكتلة الجديدة
        return addBlock(aTransactionsList)
    }

    # الحصول على المعاملات ذات الأولوية
    private func getPriorityTransactions() {
        aPriorityTransactions = []
        
        for oTransaction in aTransactions {
            if isHighPriorityTransaction(oTransaction) {
                add(aPriorityTransactions, oTransaction)
            }
        }
        
        return aPriorityTransactions
    }

    # التحقق من أولوية المعاملة
    private func isHighPriorityTransaction(oTransaction) {
        # يمكن تحديد الأولوية بناءً على:
        # - قيمة المعاملة
        # - أقدمية المعاملة
        # - تصنيف المستخدم
        
        if oTransaction.getAmount() > 1000 return true  # معاملات كبيرة
        if time() - oTransaction.getTimestamp() > 3600 return true  # معاملات قديمة
        if isVIPUser(oTransaction.getSender()) return true  # مستخدمين VIP
        
        return false
    }

    # التحقق من حالة مستخدم VIP
    private func isVIPUser(cUserId) {
        # يمكن تنفيذ منطق تحديد المستخدمين المميزين هنا
        return false  # مؤقتاً
    }

    # التحقق من صحة السلسلة بأكملها
    func validateChain() {
        if len(aBlocks) <= 1 {
            return true
        }

        for i = 2 to len(aBlocks) {
            oPreviousBlock = aBlocks[i-1]
            oCurrentBlock = aBlocks[i]

            # التحقق من صحة الربط
            if oCurrentBlock.getPreviousHash() != oPreviousBlock.getHash() {
                oLogger.error("خطأ في تسلسل الكتل عند الكتلة رقم: " + i)
                return false
            }

            # التحقق من صحة الهاش
            if not oCurrentBlock.validateHash() {
                oLogger.error("هاش غير صالح في الكتلة رقم: " + i)
                return false
            }
        }

        return true
    }

    # إرسال إشعارات للمستخدمين
    private func notifyUsers(oBlock) {
        aTransactionsList = oBlock.getTransactions()
        for oTransaction in aTransactionsList {
            # إنشاء إشعار للمرسل
            oSenderNotification = new Notification(
                oTransaction.getSender(),
                "transaction",
                "تم تأكيد إرسال " + oTransaction.getAmount() + " إلى " + oTransaction.getReceiver()
            )
            oSenderNotification.setSource(oTransaction.getId(), "transaction")
            addNotification(oSenderNotification)

            # إنشاء إشعار للمستلم
            oReceiverNotification = new Notification(
                oTransaction.getReceiver(),
                "transaction",
                "تم استلام " + oTransaction.getAmount() + " من " + oTransaction.getSender()
            )
            oReceiverNotification.setSource(oTransaction.getId(), "transaction")
            addNotification(oReceiverNotification)
        }
    }

    # دوال المساعدة

    # إنشاء كتلة البداية
    private func addGenesisBlock() {
        oGenesisBlock = new Block()
        oGenesisBlock.setPreviousHash("0")
        oGenesisBlock.setTransactions([])
        oGenesisBlock.setDifficulty(nDifficulty)
        oGenesisBlock.mine()

        add(aBlocks, oGenesisBlock)
        cLastHash = oGenesisBlock.getHash()
        oLogger.info("تم إنشاء كتلة البداية بنجاح")
    }

    # الحصول على آخر كتلة
    func getLastBlock() {
        if len(aBlocks) = 0 return NULL
        return aBlocks[len(aBlocks)]
    }

    # التحقق من صحة كتلة
    private func isValidBlock(oBlock) {
        if not isobject(oBlock) return false
        if not oBlock.validateHash() return false
        if oBlock.getPreviousHash() != getLastBlock().getHash() return false
        return true
    }

    # التحقق من صحة معاملة
    private func isValidTransaction(oTransaction) {
        if not isobject(oTransaction) return false
        if not oTransaction.isValid() return false
        return true
    }

    # التحقق من صحة قائمة المعاملات
    private func isValidTransactionsList(aList) {
        if not islist(aList) return false
        for oTransaction in aList {
            if not isValidTransaction(oTransaction) return false
        }
        return true
    }

    # دوال الحصول على المعلومات
    func getBlocksCount() { return len(aBlocks) }
    func getPendingTransactionsCount() { return len(aTransactions) }
    func getDifficulty() { return nDifficulty }
    func getStatus() { return cStatus }

    # تحويل المتحكم إلى قائمة
    func toList() {
        aData = []
        aData[:blocks] = []
        for oBlock in aBlocks {
            add(aData[:blocks], oBlock.toList())
        }
        aData[:pending_transactions] = []
        for oTransaction in aTransactions {
            add(aData[:pending_transactions], oTransaction.toList())
        }
        aData[:difficulty] = nDifficulty
        aData[:last_hash] = cLastHash
        aData[:status] = cStatus
        return aData
    }

    # تحميل المتحكم من قائمة
    func fromList(aData) {
        aBlocks = []
        for aBlockData in aData[:blocks] {
            oBlock = new Block()
            oBlock.fromList(aBlockData)
            add(aBlocks, oBlock)
        }
        aTransactions = []
        for aTransactionData in aData[:pending_transactions] {
            oTransaction = new Transaction()
            oTransaction.fromList(aTransactionData)
            add(aTransactions, oTransaction)
        }
        nDifficulty = aData[:difficulty]
        cLastHash = aData[:last_hash]
        cStatus = aData[:status]
    }
}
