class Posts {
    constructor(api, auth) {
        this.api = api;
        this.auth = auth;
        this.initEventListeners();
    }

    initEventListeners() {
        document.getElementById('postForm').addEventListener('submit', (e) => this.handleCreatePost(e));
        document.getElementById('postMedia').addEventListener('change', (e) => this.handleMediaUpload(e));
    }

    async handleCreatePost(event) {
        event.preventDefault();
        
        const content = document.getElementById('postContent').value;
        const mediaUrl = document.getElementById('postMedia').dataset.url || '';

        try {
            const response = await this.api.createPost(
                this.auth.currentUser.id,
                content,
                mediaUrl
            );

            if (response.success) {
                this.showSuccessMessage('تم نشر المنشور بنجاح');
                document.getElementById('postForm').reset();
                await this.loadFeed();
            } else {
                this.showErrorMessage(response.error);
            }
        } catch (error) {
            this.showErrorMessage('حدث خطأ أثناء نشر المنشور');
        }
    }

    async handleMediaUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        // هنا يمكن إضافة رفع الملف إلى خدمة تخزين
        // وتخزين رابط الملف في dataset.url
        // مثال:
        // const url = await uploadFile(file);
        // document.getElementById('postMedia').dataset.url = url;
    }

    async loadFeed() {
        try {
            const response = await this.api.getFeed(this.auth.currentUser.id);
            
            if (response.success) {
                this.renderPosts(response.data);
            } else {
                this.showErrorMessage(response.error);
            }
        } catch (error) {
            this.showErrorMessage('حدث خطأ أثناء تحميل المنشورات');
        }
    }

    renderPosts(posts) {
        const container = document.getElementById('postsContainer');
        container.innerHTML = '';

        posts.forEach(post => {
            container.appendChild(this.createPostElement(post));
        });
    }

    createPostElement(post) {
        const element = document.createElement('div');
        element.className = 'post';
        element.innerHTML = `
            <div class="post-header">
                <img src="${post.avatar || 'images/default-avatar.png'}" class="post-avatar" alt="صورة المستخدم">
                <div>
                    <div class="post-user">${post.username}</div>
                    <div class="post-time">${this.formatDate(post.created_at)}</div>
                </div>
            </div>
            <div class="post-content">${post.content}</div>
            ${post.media_url ? `<img src="${post.media_url}" class="post-media" alt="صورة المنشور">` : ''}
            <div class="post-stats">
                <span>${post.likes_count} إعجاب</span>
                <span>${post.comments_count} تعليق</span>
                <span>${post.shares_count} مشاركة</span>
            </div>
            <div class="post-actions">
                <button onclick="handleLike('${post.id}')" class="like-btn">
                    <i class="fas fa-heart"></i>
                    إعجاب
                </button>
                <button onclick="handleComment('${post.id}')" class="comment-btn">
                    <i class="fas fa-comment"></i>
                    تعليق
                </button>
                <button onclick="handleShare('${post.id}')" class="share-btn">
                    <i class="fas fa-share"></i>
                    مشاركة
                </button>
            </div>
        `;

        return element;
    }

    async handleLike(postId) {
        try {
            const response = await this.api.likePost(
                this.auth.currentUser.id,
                postId
            );

            if (response.success) {
                await this.loadFeed();
            } else {
                this.showErrorMessage(response.error);
            }
        } catch (error) {
            this.showErrorMessage('حدث خطأ أثناء الإعجاب بالمنشور');
        }
    }

    async handleComment(postId) {
        const content = prompt('أدخل تعليقك:');
        if (!content) return;

        try {
            const response = await this.api.addComment(
                this.auth.currentUser.id,
                postId,
                content
            );

            if (response.success) {
                await this.loadFeed();
            } else {
                this.showErrorMessage(response.error);
            }
        } catch (error) {
            this.showErrorMessage('حدث خطأ أثناء إضافة التعليق');
        }
    }

    async handleShare(postId) {
        try {
            const response = await this.api.sharePost(
                this.auth.currentUser.id,
                postId
            );

            if (response.success) {
                this.showSuccessMessage('تمت مشاركة المنشور بنجاح');
                await this.loadFeed();
            } else {
                this.showErrorMessage(response.error);
            }
        } catch (error) {
            this.showErrorMessage('حدث خطأ أثناء مشاركة المنشور');
        }
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    showSuccessMessage(message) {
        // يمكن استخدام مكتبة للإشعارات مثل toastr
        alert(message);
    }

    showErrorMessage(message) {
        // يمكن استخدام مكتبة للإشعارات مثل toastr
        alert(message);
    }
}
