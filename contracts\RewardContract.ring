# عقد المكافآت الخاص بـ SocialBank
# يدير نظام المكافآت للمستخدمين النشطين في المنصة

load "openssl.ring"
load "TokenContract.ring"

class RewardContract {
    # المتغيرات الخاصة بالعقد
    private {
        cOwner = ""                   # مالك العقد
        oTokenContract = NULL          # عقد العملة
        aRewardRates = []             # معدلات المكافآت
        aRewardTypes = []             # أنواع المكافآت المدعومة
        aRewardData = []              # بيانات المكافآت
        aUsers = []                   # قائمة المستخدمين
        cStatus = "active"            # حالة العقد
        nMinimumInterval = 24 * 60 * 60  # 24 ساعة بالثواني
        aOperations = []              # عمليات المكافآت
    }

    # دالة البناء
    func init(cOwnerAddress, cTokenContractAddress) {
        cOwner = cOwnerAddress
        oTokenContract = new TokenContract(cTokenContractAddress)
        
        # تعيين معدلات المكافآت الافتراضية
        aRewardRates = [
            :post = [ :amount = 10 * (10 ^ 18), :description = "مكافأة النشر" ],
            :like = [ :amount = 1 * (10 ^ 18), :description = "مكافأة الإعجاب" ],
            :comment = [ :amount = 5 * (10 ^ 18), :description = "مكافأة التعليق" ],
            :share = [ :amount = 8 * (10 ^ 18), :description = "مكافأة المشاركة" ]
        ]
        
        # تخزين أنواع النشاطات المدعومة
        aRewardTypes = ["post", "like", "comment", "share"]
    }

    # دوال الحصول على المعلومات
    func getOwner() { return cOwner }
    func getStatus() { return cStatus }
    func getMinimumInterval() { return nMinimumInterval }
    func getRewardTypes() { return aRewardTypes }

    # مكافأة المستخدم على نشاط معين
    func rewardUser(cUserAddress, cActivityType) {
        if not isValidReward(cUserAddress, cActivityType) return false

        nAmount = getRewardRate(cActivityType)
        if nAmount <= 0 return false

        # تحويل المكافأة
        if oTokenContract.transfer(cOwner, cUserAddress, nAmount) {
            # تحديث بيانات المكافأة
            updateRewardData(cUserAddress, cActivityType, nAmount)
            # تسجيل العملية
            addRewardOperation(cUserAddress, cActivityType, nAmount)
            return true
        }
        return false
    }

    # تعيين معدل مكافأة جديد لنشاط معين
    func setRewardRate(cActivityType, nAmount, cDescription) {
        if not isValidRewardUpdate(cActivityType, nAmount) return false

        # تحديث أو إضافة معدل المكافأة
        aRewardRates[cActivityType] = [
            :amount = nAmount,
            :description = cDescription
        ]

        # إضافة النوع إذا كان جديداً
        if not find(aRewardTypes, cActivityType) {
            add(aRewardTypes, cActivityType)
        }

        # تسجيل العملية
        addRewardOperation("system", "rate_update", nAmount, [
            :activity_type = cActivityType,
            :description = cDescription
        ])

        return true
    }

    # إرجاع معدل المكافأة لنشاط معين
    func getRewardRate(cActivityType) {
        if not isValidActivity(cActivityType) return 0
        return aRewardRates[cActivityType][:amount]
    }

    # إرجاع إحصائيات المكافآت للمستخدم
    func getUserStats(cUserAddress) {
        nUserIndex = find(aUsers, cUserAddress)
        if nUserIndex = 0 return NULL

        return aRewardData[nUserIndex]
    }

    # دوال المساعدة

    # التحقق من صحة المكافأة
    private func isValidReward(cUserAddress, cActivityType) {
        if not isAddress(cUserAddress) return false
        if not isValidActivity(cActivityType) return false
        if cStatus != "active" return false
        if not canReward(cUserAddress) return false
        return true
    }

    # التحقق من صحة تحديث المكافأة
    private func isValidRewardUpdate(cActivityType, nAmount) {
        if not isOwner() return false
        if len(cActivityType) < 1 return false
        if nAmount < 0 return false
        if cStatus != "active" return false
        return true
    }

    # التحقق من صحة نوع النشاط
    private func isValidActivity(cActivityType) {
        return find(aRewardTypes, cActivityType) > 0
    }

    # التحقق من إمكانية مكافأة المستخدم
    private func canReward(cUserAddress) {
        nUserIndex = find(aUsers, cUserAddress)
        if nUserIndex = 0 return true
        
        cLastReward = aRewardData[nUserIndex][:last_reward]
        nTimeDiff = timeDiff(cLastReward, date() + " " + time())
        return nTimeDiff >= nMinimumInterval
    }

    # تحديث بيانات المكافأة
    private func updateRewardData(cUserAddress, cActivityType, nAmount) {
        nUserIndex = find(aUsers, cUserAddress)
        if nUserIndex = 0 {
            add(aUsers, cUserAddress)
            add(aRewardData, [
                :total_rewards = nAmount,
                :activities = [],
                :last_reward = date() + " " + time(),
                :status = "active"
            ])
            nUserIndex = len(aUsers)
        }

        # تحديث البيانات
        aRewardData[nUserIndex][:total_rewards] += nAmount
        add(aRewardData[nUserIndex][:activities], [
            :type = cActivityType,
            :amount = nAmount,
            :timestamp = date() + " " + time()
        ])
        aRewardData[nUserIndex][:last_reward] = date() + " " + time()
    }

    # تسجيل عملية مكافأة
    private func addRewardOperation(cUserAddress, cActivityType, nAmount, aMetadata = []) {
        add(aOperations, [
            :user = cUserAddress,
            :activity = cActivityType,
            :amount = nAmount,
            :timestamp = date() + " " + time(),
            :block = getCurrentBlock(),
            :status = "completed",
            :metadata = aMetadata
        ])
    }

    # تحويل العقد إلى قائمة
    func toList() {
        aData = []
        aData[:owner] = cOwner
        aData[:status] = cStatus
        aData[:minimum_interval] = nMinimumInterval
        aData[:reward_types] = aRewardTypes
        aData[:reward_rates] = aRewardRates
        aData[:users] = aUsers
        aData[:reward_data] = aRewardData
        aData[:operations] = aOperations
        return aData
    }

    # تحميل العقد من قائمة
    func fromList(aData) {
        cOwner = aData[:owner]
        cStatus = aData[:status]
        nMinimumInterval = aData[:minimum_interval]
        aRewardTypes = aData[:reward_types]
        aRewardRates = aData[:reward_rates]
        aUsers = aData[:users]
        aRewardData = aData[:reward_data]
        aOperations = aData[:operations]
    }
}
