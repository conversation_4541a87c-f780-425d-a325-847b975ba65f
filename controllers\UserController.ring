# متحكم المستخدمين - يدير العمليات المتعلقة بالمستخدمين
# يتضمن إنشاء وتحديث وحذف المستخدمين، والعلاقات بينهم

load "../models/User.ring"
load "../models/Notification.ring"
load "../utils/Validator.ring"
load "../utils/Logger.ring"

class UserController {
    # المتغيرات الخاصة بالمتحكم
    private {
        aUsers = []         # قائمة المستخدمين
        oLogger = NULL      # كائن التسجيل
        cStatus = "active"  # حالة المتحكم
    }

    # دالة البناء
    func init() {
        oLogger = new Logger("users")
    }

    # إضافة مستخدم جديد
    func addUser(oUser) {
        if not isValidUser(oUser) {
            oLogger.error("بيانات المستخدم غير صالحة")
            return false
        }

        # التحقق من عدم تكرار البريد الإلكتروني
        if findUserByEmail(oUser.getEmail()) != NULL {
            oLogger.error("البريد الإلكتروني مستخدم بالفعل")
            return false
        }

        add(aUsers, oUser)
        oLogger.info("تم إضافة مستخدم جديد: " + oUser.getId())
        return true
    }

    # تحديث مستخدم
    func updateUser(cUserId, oUpdatedUser) {
        nIndex = findUserIndex(cUserId)
        if nIndex = 0 {
            oLogger.error("المستخدم غير موجود: " + cUserId)
            return false
        }

        if not isValidUser(oUpdatedUser) {
            oLogger.error("بيانات التحديث غير صالحة")
            return false
        }

        # التحقق من عدم تكرار البريد الإلكتروني
        oExistingUser = findUserByEmail(oUpdatedUser.getEmail())
        if oExistingUser != NULL and oExistingUser.getId() != cUserId {
            oLogger.error("البريد الإلكتروني مستخدم بالفعل")
            return false
        }

        aUsers[nIndex] = oUpdatedUser
        oLogger.info("تم تحديث المستخدم: " + cUserId)
        return true
    }

    # حذف مستخدم
    func deleteUser(cUserId) {
        nIndex = findUserIndex(cUserId)
        if nIndex = 0 {
            oLogger.error("المستخدم غير موجود: " + cUserId)
            return false
        }

        del(aUsers, nIndex)
        oLogger.info("تم حذف المستخدم: " + cUserId)
        return true
    }

    # متابعة مستخدم
    func followUser(cFollowerId, cFollowedId) {
        oFollower = getUser(cFollowerId)
        oFollowed = getUser(cFollowedId)

        if oFollower = NULL or oFollowed = NULL {
            oLogger.error("المستخدم غير موجود")
            return false
        }

        # التحقق من عدم وجود المتابعة مسبقاً
        if oFollower.isFollowing(cFollowedId) {
            oLogger.error("المتابعة موجودة بالفعل")
            return false
        }

        # إضافة المتابعة
        oFollower.addFollowing(cFollowedId)
        oFollowed.addFollower(cFollowerId)

        # إرسال إشعار للمستخدم المُتابَع
        notifyFollowed(oFollower, oFollowed)

        oLogger.info("تمت إضافة متابعة جديدة: " + cFollowerId + " -> " + cFollowedId)
        return true
    }

    # إلغاء متابعة مستخدم
    func unfollowUser(cFollowerId, cFollowedId) {
        oFollower = getUser(cFollowerId)
        oFollowed = getUser(cFollowedId)

        if oFollower = NULL or oFollowed = NULL {
            oLogger.error("المستخدم غير موجود")
            return false
        }

        # التحقق من وجود المتابعة
        if not oFollower.isFollowing(cFollowedId) {
            oLogger.error("المتابعة غير موجودة")
            return false
        }

        # إزالة المتابعة
        oFollower.removeFollowing(cFollowedId)
        oFollowed.removeFollower(cFollowerId)

        oLogger.info("تم إلغاء المتابعة: " + cFollowerId + " -> " + cFollowedId)
        return true
    }

    # الحصول على مستخدم
    func getUser(cUserId) {
        nIndex = findUserIndex(cUserId)
        if nIndex = 0 return NULL
        return aUsers[nIndex]
    }

    # البحث عن مستخدم بالبريد الإلكتروني
    func findUserByEmail(cEmail) {
        for oUser in aUsers {
            if oUser.getEmail() = cEmail {
                return oUser
            }
        }
        return NULL
    }

    # الحصول على متابِعي مستخدم
    func getUserFollowers(cUserId) {
        oUser = getUser(cUserId)
        if oUser = NULL return []
        
        aFollowersList = []
        for cFollowerId in oUser.getFollowers() {
            oFollower = getUser(cFollowerId)
            if oFollower != NULL {
                add(aFollowersList, oFollower)
            }
        }
        return aFollowersList
    }

    # الحصول على متابَعي مستخدم
    func getUserFollowing(cUserId) {
        oUser = getUser(cUserId)
        if oUser = NULL return []
        
        aFollowingList = []
        for cFollowingId in oUser.getFollowing() {
            oFollowing = getUser(cFollowingId)
            if oFollowing != NULL {
                add(aFollowingList, oFollowing)
            }
        }
        return aFollowingList
    }

    # دوال المساعدة

    # البحث عن مؤشر المستخدم
    private func findUserIndex(cUserId) {
        for i = 1 to len(aUsers) {
            if aUsers[i].getId() = cUserId {
                return i
            }
        }
        return 0
    }

    # التحقق من صحة مستخدم
    private func isValidUser(oUser) {
        if not isobject(oUser) return false
        if not oUser.isValid() return false
        return true
    }

    # إرسال إشعار للمستخدم المُتابَع
    private func notifyFollowed(oFollower, oFollowed) {
        oNotification = new Notification(
            oFollowed.getId(),
            "follow",
            oFollower.getName() + " قام بمتابعتك"
        )
        oNotification.setSource(oFollower.getId(), "follow")
        addNotification(oNotification)
    }

    # تحويل المتحكم إلى قائمة
    func toList() {
        aData = []
        aData[:users] = []
        for oUser in aUsers {
            add(aData[:users], oUser.toList())
        }
        aData[:status] = cStatus
        return aData
    }

    # تحميل المتحكم من قائمة
    func fromList(aData) {
        aUsers = []
        for aUserData in aData[:users] {
            oUser = new User()
            oUser.fromList(aUserData)
            add(aUsers, oUser)
        }
        cStatus = aData[:status]
    }
}
