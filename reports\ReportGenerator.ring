# مولد التقارير - يدير إنشاء وتوليد التقارير المختلفة للنظام
# يدعم تقارير المستخدمين والمعاملات والمنشورات والنشاط

load "../utils/Logger.ring"
load "../models/Block.ring"
load "../models/Transaction.ring"
load "../models/User.ring"
load "../models/Post.ring"

class ReportGenerator {
    # المتغيرات الخاصة
    private {
        oLogger = NULL          # كائن التسجيل
        cOutputDir = "reports/output/"  # مجلد حفظ التقارير
    }

    # دالة البناء
    func init() {
        oLogger = new Logger("reports")
        # إنشاء مجلد المخرجات إذا لم يكن موجوداً
        if not isDirectory(cOutputDir) {
            System("mkdir " + cOutputDir)
        }
    }

    # توليد تقرير نشاط المستخدم
    func generateUserActivityReport(oUser, cStartDate, cEndDate) {
        try {
            cReportName = "user_activity_" + oUser.getId() + "_" + 
                         substr(cStartDate, "/", "") + "_" + 
                         substr(cEndDate, "/", "") + ".txt"
            
            cContent = "تقرير نشاط المستخدم" + nl
            cContent += "====================" + nl + nl
            cContent += "معرف المستخدم: " + oUser.getId() + nl
            cContent += "اسم المستخدم: " + oUser.getName() + nl
            cContent += "البريد الإلكتروني: " + oUser.getEmail() + nl
            cContent += "تاريخ التقرير: " + date() + nl
            cContent += "الفترة: من " + cStartDate + " إلى " + cEndDate + nl + nl
            
            # إحصائيات المتابعة
            cContent += "إحصائيات المتابعة:" + nl
            cContent += "عدد المتابِعين: " + len(oUser.getFollowers()) + nl
            cContent += "عدد المتابَعين: " + len(oUser.getFollowing()) + nl + nl
            
            # إحصائيات المنشورات
            aPosts = oUser.getPosts()
            cContent += "إحصائيات المنشورات:" + nl
            cContent += "عدد المنشورات: " + len(aPosts) + nl
            nTotalLikes = 0
            nTotalComments = 0
            for oPost in aPosts {
                nTotalLikes += len(oPost.getLikes())
                nTotalComments += len(oPost.getComments())
            }
            cContent += "مجموع الإعجابات: " + nTotalLikes + nl
            cContent += "مجموع التعليقات: " + nTotalComments + nl + nl
            
            # إحصائيات المعاملات
            aTransactions = oUser.getTransactions()
            cContent += "إحصائيات المعاملات:" + nl
            nSentAmount = 0
            nReceivedAmount = 0
            for oTransaction in aTransactions {
                if oTransaction.getSender() = oUser.getId() {
                    nSentAmount += oTransaction.getAmount()
                }
                if oTransaction.getReceiver() = oUser.getId() {
                    nReceivedAmount += oTransaction.getAmount()
                }
            }
            cContent += "عدد المعاملات: " + len(aTransactions) + nl
            cContent += "المبلغ المرسل: " + nSentAmount + nl
            cContent += "المبلغ المستلم: " + nReceivedAmount + nl
            cContent += "الرصيد الصافي: " + (nReceivedAmount - nSentAmount) + nl
            
            # حفظ التقرير
            fh = fopen(cOutputDir + cReportName, "w")
            fwrite(fh, cContent)
            fclose(fh)
            
            oLogger.info("تم إنشاء تقرير نشاط المستخدم: " + cReportName)
            return true
        catch
            oLogger.error("خطأ في إنشاء تقرير نشاط المستخدم: " + cCatchError)
            return false
        }
    }

    # توليد تقرير المعاملات
    func generateTransactionReport(aTransactions, cStartDate, cEndDate) {
        try {
            cReportName = "transactions_" + 
                         substr(cStartDate, "/", "") + "_" + 
                         substr(cEndDate, "/", "") + ".txt"
            
            cContent = "تقرير المعاملات" + nl
            cContent += "===============" + nl + nl
            cContent += "تاريخ التقرير: " + date() + nl
            cContent += "الفترة: من " + cStartDate + " إلى " + cEndDate + nl + nl
            
            # إحصائيات عامة
            nTotalAmount = 0
            nMaxAmount = 0
            nMinAmount = 999999999
            for oTransaction in aTransactions {
                nAmount = oTransaction.getAmount()
                nTotalAmount += nAmount
                if nAmount > nMaxAmount { nMaxAmount = nAmount }
                if nAmount < nMinAmount { nMinAmount = nAmount }
            }
            
            cContent += "إحصائيات عامة:" + nl
            cContent += "عدد المعاملات: " + len(aTransactions) + nl
            cContent += "إجمالي المبالغ: " + nTotalAmount + nl
            cContent += "متوسط المبلغ: " + (nTotalAmount / max(len(aTransactions), 1)) + nl
            cContent += "أكبر مبلغ: " + nMaxAmount + nl
            cContent += "أصغر مبلغ: " + nMinAmount + nl + nl
            
            # تفاصيل المعاملات
            cContent += "تفاصيل المعاملات:" + nl
            for oTransaction in aTransactions {
                cContent += "معرف المعاملة: " + oTransaction.getId() + nl
                cContent += "المرسل: " + oTransaction.getSender() + nl
                cContent += "المستلم: " + oTransaction.getReceiver() + nl
                cContent += "المبلغ: " + oTransaction.getAmount() + nl
                cContent += "التاريخ: " + oTransaction.getDate() + nl
                cContent += "الحالة: " + oTransaction.getStatus() + nl
                cContent += "-------------------" + nl
            }
            
            # حفظ التقرير
            fh = fopen(cOutputDir + cReportName, "w")
            fwrite(fh, cContent)
            fclose(fh)
            
            oLogger.info("تم إنشاء تقرير المعاملات: " + cReportName)
            return true
        catch
            oLogger.error("خطأ في إنشاء تقرير المعاملات: " + cCatchError)
            return false
        }
    }

    # توليد تقرير نشاط المنصة
    func generatePlatformReport() {
        try {
            cReportName = "platform_activity_" + substr(date(), "/", "") + ".txt"
            
            cContent = "تقرير نشاط المنصة" + nl
            cContent += "=================" + nl + nl
            cContent += "تاريخ التقرير: " + date() + nl + nl
            
            # إحصائيات المستخدمين
            aUsers = getAllUsers()
            cContent += "إحصائيات المستخدمين:" + nl
            cContent += "عدد المستخدمين: " + len(aUsers) + nl
            nActiveUsers = 0
            for oUser in aUsers {
                if oUser.isActive() { nActiveUsers++ }
            }
            cContent += "المستخدمون النشطون: " + nActiveUsers + nl + nl
            
            # إحصائيات المنشورات
            aPosts = getAllPosts()
            cContent += "إحصائيات المنشورات:" + nl
            cContent += "عدد المنشورات: " + len(aPosts) + nl
            nTotalLikes = 0
            nTotalComments = 0
            for oPost in aPosts {
                nTotalLikes += len(oPost.getLikes())
                nTotalComments += len(oPost.getComments())
            }
            cContent += "مجموع الإعجابات: " + nTotalLikes + nl
            cContent += "مجموع التعليقات: " + nTotalComments + nl + nl
            
            # إحصائيات البلوكتشين
            aBlocks = getAllBlocks()
            aTransactions = getAllTransactions()
            cContent += "إحصائيات البلوكتشين:" + nl
            cContent += "عدد الكتل: " + len(aBlocks) + nl
            cContent += "عدد المعاملات: " + len(aTransactions) + nl
            nTotalAmount = 0
            for oTransaction in aTransactions {
                nTotalAmount += oTransaction.getAmount()
            }
            cContent += "إجمالي المبالغ المتداولة: " + nTotalAmount + nl
            
            # حفظ التقرير
            fh = fopen(cOutputDir + cReportName, "w")
            fwrite(fh, cContent)
            fclose(fh)
            
            oLogger.info("تم إنشاء تقرير نشاط المنصة: " + cReportName)
            return true
        catch
            oLogger.error("خطأ في إنشاء تقرير نشاط المنصة: " + cCatchError)
            return false
        }
    }

    # توليد تقرير الأداء
    func generatePerformanceReport(cStartDate, cEndDate) {
        try {
            cReportName = "performance_" + 
                         substr(cStartDate, "/", "") + "_" + 
                         substr(cEndDate, "/", "") + ".txt"
            
            cContent = "تقرير الأداء" + nl
            cContent += "===========" + nl + nl
            cContent += "تاريخ التقرير: " + date() + nl
            cContent += "الفترة: من " + cStartDate + " إلى " + cEndDate + nl + nl
            
            # إحصائيات الأداء
            cContent += "إحصائيات الأداء:" + nl
            
            # معدل إنشاء الكتل
            aBlocks = getBlocksInPeriod(cStartDate, cEndDate)
            nDays = diffDays(cStartDate, cEndDate)
            nBlockRate = len(aBlocks) / max(nDays, 1)
            cContent += "معدل إنشاء الكتل اليومي: " + nBlockRate + nl
            
            # معدل المعاملات
            aTransactions = getTransactionsInPeriod(cStartDate, cEndDate)
            nTransactionRate = len(aTransactions) / max(nDays, 1)
            cContent += "معدل المعاملات اليومي: " + nTransactionRate + nl
            
            # متوسط وقت معالجة المعاملات
            nTotalProcessingTime = 0
            for oTransaction in aTransactions {
                nTotalProcessingTime += oTransaction.getProcessingTime()
            }
            nAvgProcessingTime = nTotalProcessingTime / max(len(aTransactions), 1)
            cContent += "متوسط وقت معالجة المعاملات: " + nAvgProcessingTime + " ثانية" + nl
            
            # معدل نمو المستخدمين
            aNewUsers = getNewUsersInPeriod(cStartDate, cEndDate)
            nUserGrowthRate = len(aNewUsers) / max(nDays, 1)
            cContent += "معدل نمو المستخدمين اليومي: " + nUserGrowthRate + nl
            
            # معدل النشاط
            aPosts = getPostsInPeriod(cStartDate, cEndDate)
            nPostRate = len(aPosts) / max(nDays, 1)
            cContent += "معدل المنشورات اليومي: " + nPostRate + nl
            
            # حفظ التقرير
            fh = fopen(cOutputDir + cReportName, "w")
            fwrite(fh, cContent)
            fclose(fh)
            
            oLogger.info("تم إنشاء تقرير الأداء: " + cReportName)
            return true
        catch
            oLogger.error("خطأ في إنشاء تقرير الأداء: " + cCatchError)
            return false
        }
    }

    # دوال مساعدة

    # حساب الفرق بين تاريخين بالأيام
    private func diffDays(cDate1, cDate2) {
        try {
            aDate1 = split(cDate1, "/")
            aDate2 = split(cDate2, "/")
            
            oDate1 = new DateTime {
                year = number(aDate1[1])
                month = number(aDate1[2])
                day = number(aDate1[3])
            }
            
            oDate2 = new DateTime {
                year = number(aDate2[1])
                month = number(aDate2[2])
                day = number(aDate2[3])
            }
            
            return abs(oDate1.diffDays(oDate2))
        catch
            return 0
        }
    }
}
