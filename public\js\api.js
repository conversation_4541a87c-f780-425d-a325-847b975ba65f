// الكلاس المسؤول عن التواصل مع الخادم
class Api {
    constructor() {
        this.baseUrl = 'http://localhost:8080';
    }

    // طلبات المصادقة
    async register(username, password) {
        return this.post('/user/register', { username, password });
    }

    async login(username, password) {
        return this.post('/user/login', { username, password });
    }

    // طلبات المستخدم
    async getProfile(userId) {
        return this.get(`/user/profile?userId=${userId}`);
    }

    async updateProfile(userId, bio, avatar) {
        return this.put('/user/profile', { userId, bio, avatar });
    }

    async follow(followerId, followedId) {
        return this.post('/user/follow', { followerId, followedId });
    }

    async unfollow(followerId, followedId) {
        return this.post('/user/unfollow', { followerId, followedId });
    }

    async getFeed(userId) {
        return this.get(`/user/feed?userId=${userId}`);
    }

    // طلبات المنشورات
    async createPost(userId, content, mediaUrl) {
        return this.post('/post/create', { userId, content, mediaUrl });
    }

    async getPost(postId) {
        return this.get(`/post/view?postId=${postId}`);
    }

    async likePost(userId, postId) {
        return this.post('/post/like', { userId, postId });
    }

    async unlikePost(userId, postId) {
        return this.post('/post/unlike', { userId, postId });
    }

    async sharePost(userId, postId) {
        return this.post('/post/share', { userId, postId });
    }

    async unsharePost(userId, postId) {
        return this.post('/post/unshare', { userId, postId });
    }

    async addComment(userId, postId, content) {
        return this.post('/post/comment', { userId, postId, content });
    }

    async getComments(postId) {
        return this.get(`/post/comments?postId=${postId}`);
    }

    // طلبات البلوكتشين
    async createTransaction(senderId, receiverId, amount) {
        return this.post('/blockchain/transaction', { senderId, receiverId, amount });
    }

    async mineBlock() {
        return this.post('/blockchain/mine', {});
    }

    async getBalance(userId) {
        return this.get(`/blockchain/balance?userId=${userId}`);
    }

    async getTransactions(userId) {
        return this.get(`/blockchain/transactions?userId=${userId}`);
    }

    async getBlocks() {
        return this.get('/blockchain/blocks');
    }

    async verifyChain() {
        return this.get('/blockchain/verify');
    }

    // دوال مساعدة للطلبات
    async get(endpoint) {
        try {
            const response = await fetch(this.baseUrl + endpoint);
            return await response.json();
        } catch (error) {
            console.error('خطأ في الطلب:', error);
            throw error;
        }
    }

    async post(endpoint, data) {
        try {
            const response = await fetch(this.baseUrl + endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });
            return await response.json();
        } catch (error) {
            console.error('خطأ في الطلب:', error);
            throw error;
        }
    }

    async put(endpoint, data) {
        try {
            const response = await fetch(this.baseUrl + endpoint, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });
            return await response.json();
        } catch (error) {
            console.error('خطأ في الطلب:', error);
            throw error;
        }
    }
}
