/* متغيرات CSS */
:root {
    --primary-color: #1a73e8;
    --secondary-color: #34a853;
    --background-color: #f8f9fa;
    --text-color: #202124;
    --border-color: #dadce0;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
}

/* شريط التنقل */
.navbar {
    background-color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 4px var(--shadow-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-brand {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.nav-menu a {
    color: var(--text-color);
    text-decoration: none;
    padding: 0.5rem 1rem;
    margin: 0 0.5rem;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.nav-menu a:hover,
.nav-menu a.active {
    background-color: var(--primary-color);
    color: white;
}

/* نماذج المصادقة */
.auth-container {
    max-width: 400px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.auth-box {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-color);
}

.auth-box h2 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.auth-box input {
    width: 100%;
    padding: 0.75rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 1rem;
}

.auth-box button {
    width: 100%;
    padding: 0.75rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.auth-box button:hover {
    background-color: #1557b0;
}

.auth-box p {
    text-align: center;
    margin-top: 1rem;
}

.auth-box a {
    color: var(--primary-color);
    text-decoration: none;
}

/* قسم المنشورات */
.feed-container {
    max-width: 600px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.create-post {
    background-color: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-color);
    margin-bottom: 2rem;
}

.create-post textarea {
    width: 100%;
    min-height: 100px;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    resize: vertical;
    font-size: 1rem;
    margin-bottom: 1rem;
}

.post-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.post {
    background-color: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-color);
    margin-bottom: 1rem;
}

.post-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.post-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-left: 1rem;
}

.post-user {
    font-weight: bold;
}

.post-time {
    color: #666;
    font-size: 0.9rem;
}

.post-content {
    margin-bottom: 1rem;
}

.post-media {
    max-width: 100%;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.post-stats {
    display: flex;
    justify-content: space-around;
    padding: 0.5rem 0;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
}

.post-actions button {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 0.5rem;
    display: flex;
    align-items: center;
}

.post-actions button i {
    margin-left: 0.5rem;
}

/* قسم الملف الشخصي */
.profile-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.profile-header {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-color);
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 2rem;
    align-items: center;
    margin-bottom: 2rem;
}

.profile-avatar img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    cursor: pointer;
}

.profile-info h2 {
    margin-bottom: 0.5rem;
}

.profile-stats {
    display: flex;
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat span:first-child {
    font-size: 1.5rem;
    font-weight: bold;
    display: block;
}

/* قسم المحفظة */
.wallet-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.wallet-header {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-color);
    text-align: center;
    margin-bottom: 2rem;
}

.wallet-balance {
    font-size: 2rem;
    margin-top: 1rem;
}

.wallet-actions {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-color);
    margin-bottom: 2rem;
}

.transactions {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-color);
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    max-width: 500px;
    margin: 4rem auto;
    position: relative;
}

.close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
}

/* التحميل */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.loading::after {
    content: "";
    width: 30px;
    height: 30px;
    border: 3px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}
