# الفئة الأساسية للتحكم - تحتوي على الوظائف المشتركة بين جميع المتحكمات
class Controller {
    # المتغيرات الخاصة بالمتحكم
    private {
        oServer     # كائن الخادم
        cError      # رسالة الخطأ
    }
    
    # دالة التهيئة - تستقبل كائن الخادم وتقوم بتخزينه
    func init(server) {
        oServer = server
    }
    
    # معالجة الرد العام - تقوم بتحويل النتيجة إلى JSON وإرسالها
    func handleResponse(response, result) {
        response.setHeader("Content-Type", "application/json")
        response.write(list2json(result))
    }
    
    # معالجة حالة الخطأ - إرسال رسالة خطأ بتنسيق JSON
    func handleError(response, cMessage) {
        aError = [
            :status = "error",
            :message = cMessage
        ]
        
        handleResponse(response, aError)
    }
    
    # معالجة حالة النجاح - إرسال البيانات بتنسيق JSON
    func handleSuccess(response, data) {
        aSuccess = [
            :status = "success",
            :data = data
        ]
        
        handleResponse(response, aSuccess)
    }
    
    # دوال الوصول للمتغيرات الخاصة
    func getServer() { return oServer }    # إرجاع كائن الخادم
    func getError() { return cError }      # إرجاع رسالة الخطأ
    func setError(error) { cError = error } # تعيين رسالة الخطأ
}
