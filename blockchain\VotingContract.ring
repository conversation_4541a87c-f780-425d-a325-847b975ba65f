# عقد التصويت الذكي
# يدير عمليات التصويت والاقتراحات في المنصة

load "SmartContract.ring"

class VotingContract from SmartContract {
    private {
        # حالات الاقتراح
        aProposalStates = [ :ACTIVE = 1,
                           :PASSED = 2,
                           :REJECTED = 3,
                           :EXECUTED = 4 ]

        # الاقتراحات والأصوات
        aProposals = []
        aProposalIds = []
        aVotes = []
        aVoteKeys = []
        
        # إعدادات التصويت
        nMinVotingPeriod = 3 * 24 * 60 * 60  # 3 أيام
        nQuorum = 51  # النسبة المئوية المطلوبة للتمرير
    }

    # إنشاء اقتراح جديد
    func createProposal(cTitle, cDescription, aOptions) {
        if len(cTitle) < 5 return false
        if len(cDescription) < 20 return false
        if len(aOptions) < 2 return false

        cProposer = sender()
        cProposalId = generateProposalId(cProposer)

        # تهيئة عداد الأصوات لكل خيار
        aVoteCounts = []
        for cOption in aOptions {
            add(aVoteCounts, [ :option = cOption, :count = 0 ])
        }

        aProposal = [ :id = cProposalId,
                     :proposer = cProposer,
                     :title = cTitle,
                     :description = cDescription,
                     :options = aOptions,
                     :state = aProposalStates[:ACTIVE],
                     :createdAt = time(),
                     :endTime = time() + nMinVotingPeriod,
                     :voteCounts = aVoteCounts ]

        add(aProposals, aProposal)
        add(aProposalIds, cProposalId)
        return cProposalId
    }

    # التصويت على اقتراح
    func vote(cProposalId, cOption) {
        aProposal = getProposal(cProposalId)
        if aProposal = NULL return false
        if not canVote(aProposal) return false
        
        cVoter = sender()
        if hasVoted(cProposalId, cVoter) return false
        if not isValidOption(aProposal, cOption) return false

        # تسجيل الصوت
        cVoteKey = cProposalId + "-" + cVoter
        add(aVotes, [ :key = cVoteKey, :option = cOption ])
        add(aVoteKeys, cVoteKey)
        
        # تحديث عداد الأصوات
        for oCount in aProposal[:voteCounts] {
            if oCount[:option] = cOption {
                oCount[:count] += 1
                exit
            }
        }
        updateProposal(cProposalId, aProposal)

        # التحقق من اكتمال التصويت
        checkProposalStatus(cProposalId)
        
        return true
    }

    # تغيير الصوت
    func changeVote(cProposalId, cNewOption) {
        aProposal = getProposal(cProposalId)
        if aProposal = NULL return false
        if not canVote(aProposal) return false
        
        cVoter = sender()
        if not hasVoted(cProposalId, cVoter) return false
        if not isValidOption(aProposal, cNewOption) return false

        # استرجاع الخيار السابق
        cVoteKey = cProposalId + "-" + cVoter
        cOldOption = getVoteOption(cVoteKey)
        
        # تحديث الأصوات
        for oCount in aProposal[:voteCounts] {
            if oCount[:option] = cOldOption {
                oCount[:count] -= 1
            }
            if oCount[:option] = cNewOption {
                oCount[:count] += 1
            }
        }
        
        # تسجيل الصوت الجديد
        updateVote(cVoteKey, cNewOption)
        updateProposal(cProposalId, aProposal)

        checkProposalStatus(cProposalId)
        return true
    }

    # الحصول على تفاصيل اقتراح
    func getProposalDetails(cProposalId) {
        return getProposal(cProposalId)
    }

    # الحصول على نتائج التصويت
    func getVoteResults(cProposalId) {
        aProposal = getProposal(cProposalId)
        if aProposal = NULL return NULL
        return aProposal[:voteCounts]
    }

    # التحقق من إمكانية التصويت
    private func canVote(aProposal) {
        return aProposal[:state] = aProposalStates[:ACTIVE] and
               time() <= aProposal[:endTime]
    }

    # التحقق من صحة الخيار
    private func isValidOption(aProposal, cOption) {
        return find(aProposal[:options], cOption) > 0
    }

    # التحقق من وجود صوت سابق
    private func hasVoted(cProposalId, cVoter) {
        cVoteKey = cProposalId + "-" + cVoter
        return find(aVoteKeys, cVoteKey) > 0
    }

    # الحصول على خيار التصويت
    private func getVoteOption(cVoteKey) {
        nIndex = find(aVoteKeys, cVoteKey)
        if nIndex = 0 return NULL
        return aVotes[nIndex][:option]
    }

    # تحديث صوت
    private func updateVote(cVoteKey, cNewOption) {
        nIndex = find(aVoteKeys, cVoteKey)
        if nIndex > 0 {
            aVotes[nIndex][:option] = cNewOption
        }
    }

    # الحصول على اقتراح
    private func getProposal(cProposalId) {
        nIndex = find(aProposalIds, cProposalId)
        if nIndex = 0 return NULL
        return aProposals[nIndex]
    }

    # تحديث اقتراح
    private func updateProposal(cProposalId, aNewProposal) {
        nIndex = find(aProposalIds, cProposalId)
        if nIndex > 0 {
            aProposals[nIndex] = aNewProposal
        }
    }

    # التحقق من حالة الاقتراح
    private func checkProposalStatus(cProposalId) {
        aProposal = getProposal(cProposalId)
        if time() <= aProposal[:endTime] return
        
        nTotalVotes = 0
        for oCount in aProposal[:voteCounts] {
            nTotalVotes += oCount[:count]
        }
        
        if nTotalVotes = 0 {
            aProposal[:state] = aProposalStates[:REJECTED]
            updateProposal(cProposalId, aProposal)
            return
        }
        
        # حساب النسبة المئوية لكل خيار
        nMaxPercentage = 0
        cWinningOption = ""
        
        for oCount in aProposal[:voteCounts] {
            nPercentage = (oCount[:count] * 100) / nTotalVotes
            if nPercentage > nMaxPercentage {
                nMaxPercentage = nPercentage
                cWinningOption = oCount[:option]
            }
        }
        
        # تحديث حالة الاقتراح
        if nMaxPercentage >= nQuorum {
            aProposal[:state] = aProposalStates[:PASSED]
            aProposal[:winningOption] = cWinningOption
        } else {
            aProposal[:state] = aProposalStates[:REJECTED]
        }
        
        updateProposal(cProposalId, aProposal)
    }

    # توليد معرف فريد للاقتراح
    private func generateProposalId(cProposer) {
        cData = cProposer + string(time()) + string(random(1000000))
        return "PROP-" + substr(sha256(cData), 1, 8)
    }

    # التحقق من صحة الإجراء
    private func isValidAction(cAction) {
        switch cAction {
            case "createProposal"
                return true
            case "vote"
                return true
            case "changeVote"
                return true
            default
                return false
        }
    }

    # تنفيذ الإجراء
    private func executeAction(cAction, aParams) {
        switch cAction {
            case "createProposal"
                return createProposal(aParams[:title],
                                    aParams[:description],
                                    aParams[:options])
            case "vote"
                return vote(aParams[:proposalId],
                          aParams[:option])
            case "changeVote"
                return changeVote(aParams[:proposalId],
                                aParams[:newOption])
            default
                return false
        }
    }
}
