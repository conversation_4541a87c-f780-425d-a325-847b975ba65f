// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', () => {
    // إنشاء الكائنات الرئيسية
    const api = new Api();
    const auth = new Auth(api);
    const posts = new Posts(api, auth);
    const profile = new Profile(api, auth);
    const wallet = new Wallet(api, auth);

    // تهيئة التنقل
    initNavigation();

    // تحميل المحتوى الأولي
    auth.checkAuthStatus();
});

// دالة تهيئة التنقل
function initNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('section');

    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            
            // إخفاء جميع الأقسام
            sections.forEach(section => {
                section.style.display = 'none';
            });

            // إزالة الفئة النشطة من جميع الروابط
            navLinks.forEach(navLink => {
                navLink.classList.remove('active');
            });

            // إظهار القسم المطلوب
            const targetSection = document.getElementById(link.dataset.section);
            if (targetSection) {
                targetSection.style.display = 'block';
                link.classList.add('active');
            }
        });
    });
}

// دوال مساعدة عامة
function showLoader() {
    document.getElementById('loader').style.display = 'flex';
}

function hideLoader() {
    document.getElementById('loader').style.display = 'none';
}

function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
    }
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

// معالجة الأخطاء العامة
window.onerror = function(msg, url, lineNo, columnNo, error) {
    console.error('خطأ:', {
        message: msg,
        url: url,
        line: lineNo,
        column: columnNo,
        error: error
    });
    
    alert('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.');
    return false;
};

// تحسين تجربة المستخدم
document.addEventListener('keydown', (e) => {
    // إغلاق النوافذ المنبثقة عند الضغط على ESC
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
    }
});

// منع إعادة تحميل الصفحة عند تقديم النماذج
document.addEventListener('submit', (e) => {
    e.preventDefault();
});

// تحسين الأداء
let resizeTimeout;
window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
        // تحديث تخطيط الصفحة عند تغيير حجم النافذة
    }, 250);
});
