class Auth {
    constructor(api) {
        this.api = api;
        this.currentUser = null;
        this.initEventListeners();
        this.checkAuthStatus();
    }

    initEventListeners() {
        // نماذج تسجيل الدخول والتسجيل
        document.getElementById('loginForm').addEventListener('submit', (e) => this.handleLogin(e));
        document.getElementById('registerForm').addEventListener('submit', (e) => this.handleRegister(e));
        
        // أزرار التبديل بين النماذج
        document.getElementById('showRegister').addEventListener('click', () => this.toggleAuthForms('register'));
        document.getElementById('showLogin').addEventListener('click', () => this.toggleAuthForms('login'));
        
        // زر تسجيل الخروج
        document.getElementById('logoutBtn').addEventListener('click', () => this.handleLogout());
    }

    async handleLogin(event) {
        event.preventDefault();
        
        const username = document.getElementById('loginUsername').value;
        const password = document.getElementById('loginPassword').value;

        try {
            const response = await this.api.login(username, password);
            
            if (response.success) {
                this.setCurrentUser(response.data);
                this.showSuccessMessage('تم تسجيل الدخول بنجاح');
                this.navigateToFeed();
            } else {
                this.showErrorMessage(response.error);
            }
        } catch (error) {
            this.showErrorMessage('حدث خطأ أثناء تسجيل الدخول');
        }
    }

    async handleRegister(event) {
        event.preventDefault();
        
        const username = document.getElementById('registerUsername').value;
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (password !== confirmPassword) {
            this.showErrorMessage('كلمات المرور غير متطابقة');
            return;
        }

        try {
            const response = await this.api.register(username, password);
            
            if (response.success) {
                this.showSuccessMessage('تم إنشاء الحساب بنجاح');
                this.toggleAuthForms('login');
            } else {
                this.showErrorMessage(response.error);
            }
        } catch (error) {
            this.showErrorMessage('حدث خطأ أثناء إنشاء الحساب');
        }
    }

    handleLogout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        this.updateAuthUI();
        this.navigateToLogin();
        this.showSuccessMessage('تم تسجيل الخروج بنجاح');
    }

    setCurrentUser(user) {
        this.currentUser = user;
        localStorage.setItem('currentUser', JSON.stringify(user));
        this.updateAuthUI();
    }

    checkAuthStatus() {
        const savedUser = localStorage.getItem('currentUser');
        if (savedUser) {
            this.currentUser = JSON.parse(savedUser);
            this.updateAuthUI();
            this.navigateToFeed();
        } else {
            this.navigateToLogin();
        }
    }

    updateAuthUI() {
        const loginBtn = document.getElementById('loginBtn');
        const logoutBtn = document.getElementById('logoutBtn');
        const profileLink = document.getElementById('profileLink');
        const walletLink = document.getElementById('walletLink');

        if (this.currentUser) {
            loginBtn.style.display = 'none';
            logoutBtn.style.display = 'inline-block';
            profileLink.style.display = 'inline-block';
            walletLink.style.display = 'inline-block';
        } else {
            loginBtn.style.display = 'inline-block';
            logoutBtn.style.display = 'none';
            profileLink.style.display = 'none';
            walletLink.style.display = 'none';
        }
    }

    toggleAuthForms(form) {
        const loginBox = document.querySelector('.auth-box');
        const registerBox = document.getElementById('registerBox');

        if (form === 'register') {
            loginBox.style.display = 'none';
            registerBox.style.display = 'block';
        } else {
            loginBox.style.display = 'block';
            registerBox.style.display = 'none';
        }
    }

    navigateToLogin() {
        document.getElementById('authSection').style.display = 'block';
        document.getElementById('feedSection').style.display = 'none';
        document.getElementById('profileSection').style.display = 'none';
        document.getElementById('walletSection').style.display = 'none';
    }

    navigateToFeed() {
        document.getElementById('authSection').style.display = 'none';
        document.getElementById('feedSection').style.display = 'block';
        document.getElementById('profileSection').style.display = 'none';
        document.getElementById('walletSection').style.display = 'none';
    }

    showSuccessMessage(message) {
        // يمكن استخدام مكتبة للإشعارات مثل toastr
        alert(message);
    }

    showErrorMessage(message) {
        // يمكن استخدام مكتبة للإشعارات مثل toastr
        alert(message);
    }
}
