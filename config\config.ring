# ملف الإعدادات - يحتوي على إعدادات التطبيق
# يتضمن إعدادات قاعدة البيانات والتشفير والتسجيل

class Config {
    # إعدادات قاعدة البيانات
    DB_HOST = "localhost"
    DB_PORT = "5432"
    DB_NAME = "socialbank"
    DB_USER = "admin"
    DB_PASS = "CHANGE_THIS_PASSWORD"

    # إعدادات التشفير
    RSA_KEY_SIZE = 2048
    AES_KEY_SIZE = 256
    AES_MODE = "CBC"
    PBKDF2_ITERATIONS = 10000

    # إعدادات التسجيل
    LOG_LEVEL = "info"
    LOG_DIR = "logs"
    MAX_LOG_SIZE = 10 * 1024 * 1024  # 10 ميجابايت

    # إعدادات المصادقة
    MIN_PASSWORD_LENGTH = 8
    MAX_PASSWORD_LENGTH = 128
    MIN_USERNAME_LENGTH = 3
    MAX_USERNAME_LENGTH = 30
    TOKEN_EXPIRY = 24 * 60 * 60  # 24 ساعة

    # إعدادات الجلسة
    SESSION_TIMEOUT = 30 * 60  # 30 دقيقة
    MAX_SESSIONS_PER_USER = 5

    # إعدادات الأمان
    MAX_LOGIN_ATTEMPTS = 5
    LOGIN_LOCKOUT_TIME = 15 * 60  # 15 دقيقة
    REQUIRE_2FA = true

    # إعدادات البلوكتشين
    BLOCK_SIZE = 1000  # عدد المعاملات في الكتلة
    MINING_DIFFICULTY = 4
    MINING_REWARD = 50
    TRANSACTION_FEE = 0.1

    # إعدادات الشبكة
    NODE_PORT = 8080
    MAX_PEERS = 10
    PEER_SYNC_INTERVAL = 5 * 60  # 5 دقائق

    # دالة الحصول على الإعدادات
    func getConfig() {
        aConfig = []
        
        # استخراج جميع المتغيرات العامة
        for cKey in attributes(self) {
            if not isFunction(self[cKey]) {
                aConfig[cKey] = self[cKey]
            }
        }
        
        return aConfig
    }

    # دالة تحديث الإعدادات
    func updateConfig(aNewConfig) {
        if not isList(aNewConfig) return false
        
        for cKey, value in aNewConfig {
            if hasAttribute(self, cKey) and not isFunction(self[cKey]) {
                self[cKey] = value
            }
        }
        
        return true
    }

    # دالة التحقق من صحة الإعدادات
    func validateConfig() {
        # التحقق من إعدادات قاعدة البيانات
        if DB_HOST = "" or DB_NAME = "" or DB_USER = "" {
            return false
        }

        # التحقق من إعدادات التشفير
        if RSA_KEY_SIZE < 2048 or AES_KEY_SIZE not in [128, 192, 256] {
            return false
        }

        # التحقق من إعدادات كلمة المرور
        if MIN_PASSWORD_LENGTH < 8 or MAX_PASSWORD_LENGTH < MIN_PASSWORD_LENGTH {
            return false
        }

        # التحقق من إعدادات اسم المستخدم
        if MIN_USERNAME_LENGTH < 3 or MAX_USERNAME_LENGTH < MIN_USERNAME_LENGTH {
            return false
        }

        # التحقق من إعدادات البلوكتشين
        if BLOCK_SIZE < 1 or MINING_DIFFICULTY < 1 {
            return false
        }

        return true
    }

    # دالة تحميل الإعدادات من ملف
    func loadFromFile(cFilePath) {
        if not isFile(cFilePath) return false
        
        try {
            eval(read(cFilePath))
            return true
        catch
            return false
        }
    }

    # دالة حفظ الإعدادات في ملف
    func saveToFile(cFilePath) {
        try {
            cContent = "# إعدادات التطبيق" + nl + nl
            
            for cKey, value in getConfig() {
                if isString(value) {
                    cContent += cKey + ' = "' + value + '"' + nl
                else
                    cContent += cKey + " = " + string(value) + nl
                }
            }
            
            write(cFilePath, cContent)
            return true
        catch
            return false
        }
    }
}
