# عقد التوثيق والمشاركة في الأرباح
# يسمح للمستخدمين بتوثيق عملاتهم وكسب مكافآت

load "openssl.ring"
load "TokenContract.ring"

class StakingContract {
    # المتغيرات الخاصة بالعقد
    private {
        cOwner = ""                   # مالك العقد
        oTokenContract = NULL          # عقد العملة
        aStakes = []                  # قائمة التوثيقات
        aStakers = []                 # قائمة الموثقين
        aStakingData = []             # بيانات التوثيق
        cStatus = "active"            # حالة العقد
        nRewardRate = 10              # معدل المكافأة السنوي (10%)
        nMinStakingPeriod = 30 * 24 * 60 * 60  # 30 يوم بالثواني
        aOperations = []              # قائمة العمليات
    }

    # دالة البناء
    func init(cOwnerAddress, cTokenContractAddress) {
        cOwner = cOwnerAddress
        oTokenContract = new TokenContract(cTokenContractAddress)
    }

    # دوال الحصول على المعلومات
    func getOwner() { return cOwner }
    func getStatus() { return cStatus }
    func getRewardRate() { return nRewardRate }
    func getMinStakingPeriod() { return nMinStakingPeriod }

    # توثيق عملات
    func stake(cUserAddress, nAmount) {
        if not isValidStake(cUserAddress, nAmount) return false
        
        # تحويل العملات من المستخدم إلى العقد
        if not oTokenContract.transferFrom(cUserAddress, address(), nAmount) {
            return false
        }

        # تحديث بيانات التوثيق
        nUserIndex = find(aStakers, cUserAddress)
        if nUserIndex = 0 {
            add(aStakers, cUserAddress)
            add(aStakes, nAmount)
            add(aStakingData, [
                :rewards = 0,
                :start_time = date() + " " + time(),
                :last_claim = date() + " " + time(),
                :status = "active"
            ])
        } else {
            aStakes[nUserIndex] += nAmount
            aStakingData[nUserIndex][:last_claim] = date() + " " + time()
        }
        
        # تسجيل العملية
        addStakingOperation(cUserAddress, nAmount, "stake")
        
        return true
    }

    # سحب العملات الموثقة
    func unstake(cUserAddress, nAmount) {
        if not isValidUnstake(cUserAddress, nAmount) return false
        
        nUserIndex = find(aStakers, cUserAddress)
        
        # حساب وإضافة المكافآت المتراكمة
        calculateRewards(cUserAddress)

        # تحويل العملات المسحوبة
        if not oTokenContract.transfer(cUserAddress, nAmount) {
            return false
        }

        # تحديث البيانات
        aStakes[nUserIndex] -= nAmount
        if aStakes[nUserIndex] = 0 {
            aStakingData[nUserIndex][:status] = "inactive"
        }

        # تسجيل العملية
        addStakingOperation(cUserAddress, nAmount, "unstake")

        return true
    }

    # سحب المكافآت المتراكمة
    func claimRewards(cUserAddress) {
        if not isValidClaim(cUserAddress) return false

        nUserIndex = find(aStakers, cUserAddress)
        nRewardAmount = aStakingData[nUserIndex][:rewards]

        # تحويل المكافآت
        if not oTokenContract.transfer(cUserAddress, nRewardAmount) {
            return false
        }

        # تحديث البيانات
        aStakingData[nUserIndex][:rewards] = 0
        aStakingData[nUserIndex][:last_claim] = date() + " " + time()

        # تسجيل العملية
        addStakingOperation(cUserAddress, nRewardAmount, "claim")

        return true
    }

    # حساب المكافآت المتراكمة
    private func calculateRewards(cUserAddress) {
        nUserIndex = find(aStakers, cUserAddress)
        if nUserIndex = 0 return 0
        
        cLastClaim = aStakingData[nUserIndex][:last_claim]
        nStakingPeriod = timeDiff(cLastClaim, date() + " " + time())
        if nStakingPeriod <= 0 return 0

        # حساب المكافأة: المبلغ * المعدل * (الفترة بالثواني / ثواني السنة)
        nReward = (aStakes[nUserIndex] * nRewardRate * nStakingPeriod) / (365 * 24 * 60 * 60 * 100)
        aStakingData[nUserIndex][:rewards] += nReward
        aStakingData[nUserIndex][:last_claim] = date() + " " + time()

        return nReward
    }

    # التحقق من إمكانية السحب
    private func canUnstake(cUserAddress) {
        nUserIndex = find(aStakers, cUserAddress)
        if nUserIndex = 0 return false

        cStartTime = aStakingData[nUserIndex][:start_time]
        nStakingPeriod = timeDiff(cStartTime, date() + " " + time())
        
        return nStakingPeriod >= nMinStakingPeriod
    }

    # التحقق من صحة التوثيق
    private func isValidStake(cUserAddress, nAmount) {
        if not isAddress(cUserAddress) return false
        if nAmount <= 0 return false
        if cStatus != "active" return false
        
        return true
    }

    # التحقق من صحة السحب
    private func isValidUnstake(cUserAddress, nAmount) {
        if not isAddress(cUserAddress) return false
        if nAmount <= 0 return false
        if cStatus != "active" return false
        
        nUserIndex = find(aStakers, cUserAddress)
        if nUserIndex = 0 return false
        if aStakes[nUserIndex] < nAmount return false
        if not canUnstake(cUserAddress) return false
        
        return true
    }

    # التحقق من صحة سحب المكافآت
    private func isValidClaim(cUserAddress) {
        if not isAddress(cUserAddress) return false
        if cStatus != "active" return false
        
        nUserIndex = find(aStakers, cUserAddress)
        if nUserIndex = 0 return false
        if aStakingData[nUserIndex][:rewards] <= 0 return false
        
        return true
    }

    # تسجيل عملية توثيق
    private func addStakingOperation(cUserAddress, nAmount, cType) {
        add(aOperations, [
            :user = cUserAddress,
            :amount = nAmount,
            :type = cType,
            :timestamp = date() + " " + time(),
            :block = getCurrentBlock(),
            :status = "completed"
        ])
    }

    # تحويل العقد إلى قائمة
    func toList() {
        aData = []
        aData[:owner] = cOwner
        aData[:status] = cStatus
        aData[:reward_rate] = nRewardRate
        aData[:min_staking_period] = nMinStakingPeriod
        aData[:stakers] = aStakers
        aData[:stakes] = aStakes
        aData[:staking_data] = aStakingData
        aData[:operations] = aOperations
        return aData
    }

    # تحميل العقد من قائمة
    func fromList(aData) {
        cOwner = aData[:owner]
        cStatus = aData[:status]
        nRewardRate = aData[:reward_rate]
        nMinStakingPeriod = aData[:min_staking_period]
        aStakers = aData[:stakers]
        aStakes = aData[:stakes]
        aStakingData = aData[:staking_data]
        aOperations = aData[:operations]
    }
}
