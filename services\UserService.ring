# استيراد المكتبات والنماذج اللازمة
load "../core/Service.ring"     # الخدمة الأساسية
load "../core/Database.ring"    # قاعدة البيانات
load "../models/User.ring"      # نموذج المستخدم

# خدمة المستخدمين: تدير عمليات التسجيل، تسجيل الدخول، وإدارة الملفات الشخصية
class UserService from Service {
    # المتغيرات الخاصة بالخدمة
    private {
        oDb    # كائن قاعدة البيانات
        aUsers = []     # قائمة المستخدمين
        aFollows = []   # قائمة المتابعات
    }
    
    # دالة التهيئة: إعداد الاتصال بقاعدة البيانات
    func init() {
        oDb = new Database()
        loadData()
    }
    
    # تحميل البيانات من قاعدة البيانات
    private func loadData() {
        try {
            # تحميل المستخدمين
            aResult = oDb.execute("SELECT * FROM users ORDER BY created_at DESC")
            for oUser in aResult {
                add(aUsers, oUser)
            }
            
            # تحميل المتابعات
            aResult = oDb.execute("SELECT * FROM follows ORDER BY created_at DESC")
            for oFollow in aResult {
                add(aFollows, oFollow)
            }
        catch
            handleError(cCatchError)
        }
    }
    
    # دالة تسجيل مستخدم جديد
    func register(cUsername, cPassword) {
        try {
            # التحقق من عدم وجود مستخدم بنفس اسم المستخدم
            for oUser in aUsers {
                if oUser[:username] = cUsername {
                    return handleError("اسم المستخدم موجود بالفعل")
                }
            }
            
            # إنشاء حساب مستخدم جديد مع تشفير كلمة المرور وإنشاء المفاتيح
            oUser = new User()
            oUser.setUsername(cUsername)
            oUser.setPassword(cPassword)
            
            # تحويل بيانات المستخدم إلى قائمة
            aUserData = oUser.toList()
            
            # حفظ بيانات المستخدم في قاعدة البيانات
            oDb.execute("
                INSERT INTO users (
                    id, username, password_hash, 
                    public_key, private_key,
                    created_at
                ) VALUES (?, ?, ?, ?, ?, ?)
            ", aUserData)
            
            # إضافة المستخدم إلى القائمة المحلية
            add(aUsers, aUserData)
            
            return handleSuccess(aUserData)
        catch 
            return handleError(cCatchError)
        }
    }
    
    # دالة تسجيل الدخول
    func login(cUsername, cPassword) {
        try {
            # البحث عن المستخدم في القائمة المحلية
            for oUser in aUsers {
                if oUser[:username] = cUsername {
                    # التحقق من صحة كلمة المرور
                    oTempUser = new User()
                    oTempUser.fromList(oUser)
                    
                    if not oTempUser.verifyPassword(cPassword) {
                        return handleError("كلمة المرور غير صحيحة")
                    }
                    
                    return handleSuccess(oUser)
                }
            }
            
            return handleError("اسم المستخدم غير موجود")
        catch 
            return handleError(cCatchError)
        }
    }
    
    # دالة عرض الملف الشخصي للمستخدم مع الإحصائيات
    func getProfile(cUserId) {
        try {
            # البحث عن المستخدم في القائمة المحلية
            for oUser in aUsers {
                if oUser[:id] = cUserId {
                    # حساب الإحصائيات
                    nFollowingCount = 0
                    nFollowersCount = 0
                    
                    for oFollow in aFollows {
                        if oFollow[:follower_id] = cUserId {
                            nFollowingCount++
                        }
                        if oFollow[:followed_id] = cUserId {
                            nFollowersCount++
                        }
                    }
                    
                    # إضافة الإحصائيات للمستخدم
                    oUser[:following_count] = nFollowingCount
                    oUser[:followers_count] = nFollowersCount
                    
                    return handleSuccess(oUser)
                }
            }
            
            return handleError("المستخدم غير موجود")
        catch 
            return handleError(cCatchError)
        }
    }
    
    # دالة تحديث الملف الشخصي
    func updateProfile(cUserId, aProfileData) {
        try {
            # البحث عن المستخدم في القائمة المحلية
            for oUser in aUsers {
                if oUser[:id] = cUserId {
                    # تحديث البيانات
                    for cKey in aProfileData {
                        if cKey != "id" and cKey != "password_hash" {
                            oUser[cKey] = aProfileData[cKey]
                        }
                    }
                    
                    # تحديث قاعدة البيانات
                    oDb.execute("
                        UPDATE users 
                        SET username = ?,
                            email = ?,
                            avatar = ?,
                            bio = ?
                        WHERE id = ?
                    ", [
                        oUser[:username],
                        oUser[:email],
                        oUser[:avatar],
                        oUser[:bio],
                        cUserId
                    ])
                    
                    return handleSuccess(oUser)
                }
            }
            
            return handleError("المستخدم غير موجود")
        catch 
            return handleError(cCatchError)
        }
    }
    
    # دالة متابعة مستخدم
    func follow(cFollowerId, cFollowedId) {
        try {
            # التحقق من وجود المستخدمين
            bFollowerExists = false
            bFollowedExists = false
            
            for oUser in aUsers {
                if oUser[:id] = cFollowerId {
                    bFollowerExists = true
                }
                if oUser[:id] = cFollowedId {
                    bFollowedExists = true
                }
                if bFollowerExists and bFollowedExists {
                    break
                }
            }
            
            if not (bFollowerExists and bFollowedExists) {
                return handleError("المستخدم غير موجود")
            }
            
            # التحقق من عدم وجود متابعة مسبقة
            for oFollow in aFollows {
                if oFollow[:follower_id] = cFollowerId and 
                   oFollow[:followed_id] = cFollowedId {
                    return handleError("المتابعة موجودة بالفعل")
                }
            }
            
            # إنشاء بيانات المتابعة
            aFollowData = [
                :follower_id = cFollowerId,
                :followed_id = cFollowedId,
                :created_at = date() + " " + time()
            ]
            
            # حفظ المتابعة في قاعدة البيانات
            oDb.execute("
                INSERT INTO follows (
                    follower_id, followed_id, created_at
                ) VALUES (?, ?, ?)
            ", [
                aFollowData[:follower_id],
                aFollowData[:followed_id],
                aFollowData[:created_at]
            ])
            
            # إضافة المتابعة إلى القائمة المحلية
            add(aFollows, aFollowData)
            
            return handleSuccess(true)
        catch 
            return handleError(cCatchError)
        }
    }
    
    # دالة الحصول على قائمة المتابِعين
    func getFollowers(cUserId) {
        try {
            aFollowersList = []
            
            # جمع المتابِعين من القائمة المحلية
            for oFollow in aFollows {
                if oFollow[:followed_id] = cUserId {
                    for oUser in aUsers {
                        if oUser[:id] = oFollow[:follower_id] {
                            add(aFollowersList, oUser)
                            break
                        }
                    }
                }
            }
            
            return handleSuccess(aFollowersList)
        catch 
            return handleError(cCatchError)
        }
    }
    
    # دالة الحصول على قائمة المتابَعين
    func getFollowing(cUserId) {
        try {
            aFollowingList = []
            
            # جمع المتابَعين من القائمة المحلية
            for oFollow in aFollows {
                if oFollow[:follower_id] = cUserId {
                    for oUser in aUsers {
                        if oUser[:id] = oFollow[:followed_id] {
                            add(aFollowingList, oUser)
                            break
                        }
                    }
                }
            }
            
            return handleSuccess(aFollowingList)
        catch 
            return handleError(cCatchError)
        }
    }
}
