# تحميل مكتبة SQLite للتعامل مع قاعدة البيانات
load "sqlite.ring"

# فئة قاعدة البيانات المسؤولة عن إدارة الاتصال وتنفيذ الاستعلامات
class Database {
    private {
        # كائن الاتصال بقاعدة البيانات
        oConn
        # متغير لتخزين رسائل الخطأ
        cError
    }
    
    # دالة التهيئة: تقوم بإنشاء الاتصال بقاعدة البيانات وتهيئة الجداول
    func init() {
        try {
            oConn = sqlite_init()
            sqlite_open(oConn, "database/social_bank.db")
            createTables()
        catch 
            cError = cCatchError
        }
    }
    
    # دالة إنشاء جداول قاعدة البيانات
    func createTables() {
        try {
            # جدول المستخدمين: يخزن معلومات المستخدمين الأساسية والمفاتيح العامة والخاصة
            sqlite_execute(oConn, "
                CREATE TABLE IF NOT EXISTS users (
                    id TEXT PRIMARY KEY,
                    username TEXT UNIQUE,
                    password_hash TEXT,
                    public_key TEXT,
                    private_key TEXT,
                    bio TEXT,
                    avatar TEXT,
                    created_at TEXT
                )
            ")
            
            # جدول المتابعة: يتتبع علاقات المتابعة بين المستخدمين
            sqlite_execute(oConn, "
                CREATE TABLE IF NOT EXISTS follows (
                    follower_id TEXT,
                    followed_id TEXT,
                    created_at TEXT,
                    PRIMARY KEY (follower_id, followed_id),
                    FOREIGN KEY (follower_id) REFERENCES users(id),
                    FOREIGN KEY (followed_id) REFERENCES users(id)
                )
            ")
            
            # جدول المنشورات: يخزن منشورات المستخدمين والوسائط المرتبطة بها
            sqlite_execute(oConn, "
                CREATE TABLE IF NOT EXISTS posts (
                    id TEXT PRIMARY KEY,
                    user_id TEXT,
                    content TEXT,
                    media_url TEXT,
                    created_at TEXT,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ")
            
            # جدول الإعجابات: يتتبع إعجابات المستخدمين على المنشورات
            sqlite_execute(oConn, "
                CREATE TABLE IF NOT EXISTS likes (
                    user_id TEXT,
                    post_id TEXT,
                    created_at TEXT,
                    PRIMARY KEY (user_id, post_id),
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (post_id) REFERENCES posts(id)
                )
            ")
            
            # جدول التعليقات: يخزن تعليقات المستخدمين على المنشورات
            sqlite_execute(oConn, "
                CREATE TABLE IF NOT EXISTS comments (
                    id TEXT PRIMARY KEY,
                    post_id TEXT,
                    user_id TEXT,
                    content TEXT,
                    created_at TEXT,
                    FOREIGN KEY (post_id) REFERENCES posts(id),
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ")
            
            # جدول المشاركات: يتتبع مشاركات المستخدمين للمنشورات
            sqlite_execute(oConn, "
                CREATE TABLE IF NOT EXISTS shares (
                    user_id TEXT,
                    post_id TEXT,
                    created_at TEXT,
                    PRIMARY KEY (user_id, post_id),
                    FOREIGN KEY (user_id) REFERENCES users(id),
                    FOREIGN KEY (post_id) REFERENCES posts(id)
                )
            ")
            
            # جدول المعاملات: يخزن المعاملات المالية بين المستخدمين مع التوقيعات الرقمية
            sqlite_execute(oConn, "
                CREATE TABLE IF NOT EXISTS transactions (
                    id TEXT PRIMARY KEY,
                    sender_id TEXT,
                    receiver_id TEXT,
                    amount REAL,
                    signature TEXT,
                    block_id TEXT,
                    created_at TEXT,
                    FOREIGN KEY (sender_id) REFERENCES users(id),
                    FOREIGN KEY (receiver_id) REFERENCES users(id)
                )
            ")
            
            # جدول الكتل: يخزن معلومات سلسلة الكتل للمعاملات
            sqlite_execute(oConn, "
                CREATE TABLE IF NOT EXISTS blocks (
                    id TEXT PRIMARY KEY,
                    previous_hash TEXT,
                    merkle_root TEXT,
                    nonce INTEGER,
                    timestamp TEXT,
                    created_at TEXT
                )
            ")
            
        catch 
            cError = cCatchError
        }
    }
    
    # دالة تنفيذ الاستعلامات: تنفذ استعلام SQL مع المعاملات المحددة
    func execute(cQuery, aParams) {
        try {
            return sqlite_execute(oConn, cQuery, aParams)
        catch 
            cError = cCatchError
            return []
        }
    }
    
    # دالة إغلاق الاتصال: تغلق الاتصال بقاعدة البيانات
    func close() {
        sqlite_close(oConn)
    }
    
    # دالة استرجاع الخطأ: تعيد رسالة الخطأ الأخيرة إن وجدت
    func getError() { return cError }
}
