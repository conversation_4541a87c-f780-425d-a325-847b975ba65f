# مشغل الاختبارات - يدير تشغيل جميع اختبارات النظام
# يجمع نتائج الاختبارات ويولد تقارير مفصلة

load "../utils/Logger.ring"

class TestRunner {
    # المتغيرات الخاصة
    private {
        oLogger = NULL         # كائن التسجيل
        aTestFiles = []        # قائمة ملفات الاختبار
        nPassedTests = 0       # عدد الاختبارات الناجحة
        nFailedTests = 0       # عدد الاختبارات الفاشلة
        nTotalTime = 0        # الوقت الإجمالي للتنفيذ
    }

    # دالة البناء
    func init() {
        oLogger = new Logger("test_runner")
        
        # جمع ملفات الاختبار
        collectTestFiles()
    }

    # تشغيل جميع الاختبارات
    func runAllTests() {
        oLogger.info("بدء تشغيل الاختبارات")
        
        nStartTime = clock()
        
        # تشغيل كل ملف اختبار
        for cTestFile in aTestFiles {
            runTestFile(cTestFile)
        }
        
        nTotalTime = clock() - nStartTime
        
        # طباعة التقرير النهائي
        printSummary()
    }

    # تشغيل ملف اختبار محدد
    func runTestFile(cTestFile) {
        oLogger.info("تشغيل اختبار: " + cTestFile)
        
        try {
            # تحميل وتنفيذ ملف الاختبار
            eval(read(cTestFile))
            nPassedTests++
            oLogger.info("نجح الاختبار: " + cTestFile)
        catch
            nFailedTests++
            oLogger.error("فشل الاختبار: " + cTestFile + nl + cCatchError)
        }
    }

    # جمع ملفات الاختبار
    private func collectTestFiles() {
        # البحث عن جميع ملفات الاختبار في مجلد tests
        aFiles = dir("tests")
        
        for aFile in aFiles {
            if right(aFile[1], 4) = ".ring" and 
               left(aFile[1], 5) = "test_" and 
               aFile[1] != "test_runner.ring" {
                add(aTestFiles, "tests/" + aFile[1])
            }
        }
        
        oLogger.info("تم العثور على " + len(aTestFiles) + " ملف اختبار")
    }

    # طباعة ملخص نتائج الاختبارات
    private func printSummary() {
        see "==================================" + nl
        see "تقرير الاختبارات" + nl
        see "==================================" + nl
        see "عدد الاختبارات الكلي: " + (nPassedTests + nFailedTests) + nl
        see "الاختبارات الناجحة: " + nPassedTests + nl
        see "الاختبارات الفاشلة: " + nFailedTests + nl
        see "الوقت المستغرق: " + nTotalTime + " ثانية" + nl
        see "==================================" + nl
        
        # تسجيل النتائج
        oLogger.info("اكتمل تشغيل الاختبارات")
        oLogger.info("نجح: " + nPassedTests + ", فشل: " + nFailedTests)
    }

    # الحصول على عدد الاختبارات الناجحة
    func getPassedTests() {
        return nPassedTests
    }

    # الحصول على عدد الاختبارات الفاشلة
    func getFailedTests() {
        return nFailedTests
    }

    # الحصول على الوقت الإجمالي
    func getTotalTime() {
        return nTotalTime
    }
}

# تشغيل الاختبارات إذا تم تنفيذ الملف مباشرة
if filename() = sysargv[2] {
    oRunner = new TestRunner()
    oRunner.runAllTests()
}
