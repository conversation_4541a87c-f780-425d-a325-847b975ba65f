# مدير العقود الذكية
# يدير جميع العقود ويربط بينها

load "SmartContract.ring"
load "LoanContract.ring"
load "VotingContract.ring"
load "../contracts/TokenContract.ring"
load "../contracts/RewardContract.ring"
load "../contracts/StakingContract.ring"

class ContractManager {
    private {
        # قائمة العقود النشطة
        aContracts = []
        
        # عناوين العقود الرئيسية
        aContractAddresses = [ :token = "",
                             :reward = "",
                             :staking = "",
                             :loan = "",
                             :voting = "" ]
        
        # المالك
        cOwner = ""
    }

    # دالة البناء
    func init(cOwnerAddress) {
        cOwner = cOwnerAddress
        initializeContracts()
    }

    # تهيئة العقود الأساسية
    private func initializeContracts() {
        # إنشاء عقد العملة
        oTokenContract = new TokenContract(cOwner)
        aContractAddresses[:token] = oTokenContract.getAddress()
        add(aContracts, [ :address = aContractAddresses[:token],
                         :contract = oTokenContract ])

        # إنشاء عقد المكافآت
        oRewardContract = new RewardContract(cOwner, aContractAddresses[:token])
        aContractAddresses[:reward] = oRewardContract.getAddress()
        add(aContracts, [ :address = aContractAddresses[:reward],
                         :contract = oRewardContract ])

        # إنشاء عقد التوثيق
        oStakingContract = new StakingContract(cOwner, aContractAddresses[:token])
        aContractAddresses[:staking] = oStakingContract.getAddress()
        add(aContracts, [ :address = aContractAddresses[:staking],
                         :contract = oStakingContract ])

        # إنشاء عقد القروض
        oLoanContract = new LoanContract(cOwner)
        aContractAddresses[:loan] = oLoanContract.getAddress()
        add(aContracts, [ :address = aContractAddresses[:loan],
                         :contract = oLoanContract ])

        # إنشاء عقد التصويت
        oVotingContract = new VotingContract(cOwner)
        aContractAddresses[:voting] = oVotingContract.getAddress()
        add(aContracts, [ :address = aContractAddresses[:voting],
                         :contract = oVotingContract ])
    }

    # تنفيذ عملية على عقد معين
    func executeContract(cContractAddress, cAction, aParams) {
        for oContract in aContracts {
            if oContract[:address] = cContractAddress {
                return oContract[:contract].execute(cAction, aParams)
            }
        }
        return false
    }

    # الحصول على عنوان عقد معين
    func getContractAddress(cContractType) {
        return aContractAddresses[cContractType]
    }

    # الحصول على عقد معين
    func getContract(cContractAddress) {
        for oContract in aContracts {
            if oContract[:address] = cContractAddress {
                return oContract[:contract]
            }
        }
        return NULL
    }

    # التحقق من صحة عنوان العقد
    private func isValidContract(cContractAddress) {
        for oContract in aContracts {
            if oContract[:address] = cContractAddress {
                return true
            }
        }
        return false
    }

    # مثال على تنفيذ عملية معقدة تشمل عدة عقود
    func executeComplexOperation(cOperationType, aParams) {
        switch cOperationType {
            case "stake_and_vote"
                # توثيق العملات أولاً
                aStakeParams = [ :type = "stake",
                               :amount = aParams[:stakeAmount] ]
                if not executeContract(aContractAddresses[:staking], "stake", aStakeParams) {
                    return false
                }
                # ثم التصويت
                aVoteParams = [ :type = "vote",
                              :proposalId = aParams[:proposalId],
                              :option = aParams[:option] ]
                return executeContract(aContractAddresses[:voting], "vote", aVoteParams)

            case "loan_with_stake"
                # التحقق من التوثيق أولاً
                aStakeParams = [ :type = "stake",
                               :amount = aParams[:stakeAmount] ]
                if not executeContract(aContractAddresses[:staking], "stake", aStakeParams) {
                    return false
                }
                # ثم طلب القرض
                aLoanParams = [ :type = "loan",
                              :amount = aParams[:loanAmount],
                              :duration = aParams[:duration],
                              :collateral = aParams[:stakeAmount] ]
                return executeContract(aContractAddresses[:loan], "requestLoan", aLoanParams)

            case "reward_and_stake"
                # توزيع المكافآت أولاً
                aRewardParams = [ :type = "reward",
                                :user = aParams[:user],
                                :amount = aParams[:amount] ]
                if not executeContract(aContractAddresses[:reward], "rewardUser", aRewardParams) {
                    return false
                }
                # ثم توثيق المكافآت
                aStakeParams = [ :type = "stake",
                               :amount = aParams[:amount] ]
                return executeContract(aContractAddresses[:staking], "stake", aStakeParams)

            default
                return false
        }
    }
}
