# عقد العملة الرقمية الخاص بـ SocialBank
# يتضمن العقد وظائف إدارة العملة والتحويلات والأرصدة

load "openssl.ring"
load "../utils/Validator.ring"

class TokenContract {
    # المتغيرات الخاصة بالعقد
    private {
        cName = "SocialBank Token"    # اسم العملة
        cSymbol = "SBT"               # رمز العملة
        nDecimals = 18                # عدد الكسور العشرية
        nTotalSupply = 1000000 * (10 ^ 18)  # مليون توكن
        aBalances = []                # قائمة الأرصدة
        aHolders = []                 # قائمة حاملي العملة
        aAllowances = []              # قائمة التفويضات
        aTransactions = []            # قائمة المعاملات
        cOwner = ""                   # مالك العقد
        cStatus = "active"            # حالة العقد
    }

    # دالة البناء
    func init(cOwnerAddress) {
        cOwner = cOwnerAddress
        add(aHolders, cOwnerAddress)
        add(aBalances, nTotalSupply)
    }

    # دوال الحصول على معلومات العملة
    func getName() { return cName }           # إرجاع اسم العملة
    func getSymbol() { return cSymbol }       # إرجاع رمز العملة
    func getDecimals() { return nDecimals }   # إرجاع عدد الكسور العشرية
    func getTotalSupply() { return nTotalSupply } # إرجاع العدد الكلي للعملات
    func getStatus() { return cStatus }       # إرجاع حالة العقد
    func getOwner() { return cOwner }         # إرجاع مالك العقد

    # إرجاع رصيد عنوان معين
    func getBalanceOf(cAddress) {
        nIndex = find(aHolders, cAddress)
        if nIndex = 0 return 0
        return aBalances[nIndex]
    }

    # تحويل عملات من حساب لآخر
    func transfer(cFrom, cTo, nAmount) {
        if not isValidTransfer(cFrom, cTo, nAmount) return false
        
        nFromIndex = find(aHolders, cFrom)
        nToIndex = find(aHolders, cTo)
        
        # إذا كان المستلم جديداً، أضفه للقائمة
        if nToIndex = 0 {
            add(aHolders, cTo)
            add(aBalances, 0)
            nToIndex = len(aHolders)
        }

        # تنفيذ التحويل
        aBalances[nFromIndex] -= nAmount
        aBalances[nToIndex] += nAmount

        # تسجيل المعاملة
        addTransaction(cFrom, cTo, nAmount, "transfer")

        return true
    }

    # السماح لعنوان معين بإنفاق مبلغ محدد
    func approve(cOwnerAddress, cSpenderAddress, nAmount) {
        if not isValidApproval(cOwnerAddress, cSpenderAddress, nAmount) return false

        nAllowanceIndex = findAllowance(cOwnerAddress, cSpenderAddress)
        if nAllowanceIndex = 0 {
            add(aAllowances, [
                :owner = cOwnerAddress,
                :spender = cSpenderAddress,
                :amount = nAmount,
                :timestamp = date() + " " + time()
            ])
        } else {
            aAllowances[nAllowanceIndex][:amount] = nAmount
            aAllowances[nAllowanceIndex][:timestamp] = date() + " " + time()
        }

        # تسجيل المعاملة
        addTransaction(cOwnerAddress, cSpenderAddress, nAmount, "approve")

        return true
    }

    # إرجاع المبلغ المسموح به
    func getAllowance(cOwnerAddress, cSpenderAddress) {
        nAllowanceIndex = findAllowance(cOwnerAddress, cSpenderAddress)
        if nAllowanceIndex = 0 return 0
        return aAllowances[nAllowanceIndex][:amount]
    }

    # تحويل من حساب مسموح به
    func transferFrom(cSpenderAddress, cFrom, cTo, nAmount) {
        if not isValidTransferFrom(cSpenderAddress, cFrom, cTo, nAmount) return false

        # التحقق من التفويض
        nAllowanceIndex = findAllowance(cFrom, cSpenderAddress)
        if nAllowanceIndex = 0 or aAllowances[nAllowanceIndex][:amount] < nAmount return false

        # تنفيذ التحويل
        if not transfer(cFrom, cTo, nAmount) return false

        # تحديث التفويض
        aAllowances[nAllowanceIndex][:amount] -= nAmount

        # تسجيل المعاملة
        addTransaction(cSpenderAddress, cTo, nAmount, "transferFrom")

        return true
    }

    # دوال المساعدة

    # البحث عن تفويض
    private func findAllowance(cOwnerAddress, cSpenderAddress) {
        for i = 1 to len(aAllowances) {
            if aAllowances[i][:owner] = cOwnerAddress and 
               aAllowances[i][:spender] = cSpenderAddress {
                return i
            }
        }
        return 0
    }

    # إضافة معاملة جديدة
    private func addTransaction(cFrom, cTo, nAmount, cType) {
        add(aTransactions, [
            :from = cFrom,
            :to = cTo,
            :amount = nAmount,
            :type = cType,
            :timestamp = date() + " " + time(),
            :block = getCurrentBlock(),
            :status = "completed"
        ])
    }

    # التحقق من صحة التحويل
    private func isValidTransfer(cFrom, cTo, nAmount) {
        if not isAddress(cFrom) or not isAddress(cTo) return false
        if nAmount <= 0 return false
        if cStatus != "active" return false
        
        nFromIndex = find(aHolders, cFrom)
        if nFromIndex = 0 or aBalances[nFromIndex] < nAmount return false
        
        return true
    }

    # التحقق من صحة التفويض
    private func isValidApproval(cOwnerAddress, cSpenderAddress, nAmount) {
        if not isAddress(cOwnerAddress) or not isAddress(cSpenderAddress) return false
        if nAmount < 0 return false
        if cStatus != "active" return false
        
        return true
    }

    # التحقق من صحة التحويل من حساب مفوض
    private func isValidTransferFrom(cSpenderAddress, cFrom, cTo, nAmount) {
        if not isValidTransfer(cFrom, cTo, nAmount) return false
        if not isAddress(cSpenderAddress) return false
        
        return true
    }

    # تحويل العقد إلى قائمة
    func toList() {
        aData = []
        aData[:name] = cName
        aData[:symbol] = cSymbol
        aData[:decimals] = nDecimals
        aData[:total_supply] = nTotalSupply
        aData[:owner] = cOwner
        aData[:status] = cStatus
        aData[:holders] = aHolders
        aData[:balances] = aBalances
        aData[:allowances] = aAllowances
        aData[:transactions] = aTransactions
        return aData
    }

    # تحميل العقد من قائمة
    func fromList(aData) {
        cName = aData[:name]
        cSymbol = aData[:symbol]
        nDecimals = aData[:decimals]
        nTotalSupply = aData[:total_supply]
        cOwner = aData[:owner]
        cStatus = aData[:status]
        aHolders = aData[:holders]
        aBalances = aData[:balances]
        aAllowances = aData[:allowances]
        aTransactions = aData[:transactions]
    }
}
