# اختبارات المصادقة - للتحقق من صحة عمل نظام المصادقة
# يتضمن اختبارات التسجيل وتسجيل الدخول وإدارة المفاتيح

load "../controllers/AuthController.ring"
load "../utils/Crypto.ring"
load "../utils/Logger.ring"

# دالة الاختبار الرئيسية
func main() {
    # تهيئة المتغيرات
    oAuth = new AuthController()
    oLogger = new Logger("test_auth")
    
    oLogger.info("بدء اختبارات المصادقة")
    
    # اختبار تسجيل مستخدم جديد
    testRegistration()
    
    # اختبار تسجيل الدخول
    testLogin()
    
    # اختبار إدارة المفاتيح
    testKeyManagement()
    
    oLogger.info("انتهت اختبارات المصادقة")
}

# اختبار تسجيل مستخدم جديد
func testRegistration() {
    oLogger.info("اختبار تسجيل مستخدم جديد")
    
    # بيانات المستخدم
    cUsername = "testuser"
    cPassword = "Test@123456"
    cEmail = "<EMAIL>"
    
    # محاولة التسجيل
    oAuth = new AuthController()
    aResult = oAuth.register(cUsername, cPassword, cEmail)
    
    # التحقق من نجاح التسجيل
    if aResult[:success] {
        oLogger.info("نجح تسجيل المستخدم")
        assert(aResult[:user] != NULL)
        assert(aResult[:user].getUsername() = cUsername)
        assert(aResult[:user].getEmail() = cEmail)
        assert(aResult[:user].getPublicKey() != NULL)
    else
        oLogger.error("فشل تسجيل المستخدم: " + aResult[:error])
        assert(false)
    }
}

# اختبار تسجيل الدخول
func testLogin() {
    oLogger.info("اختبار تسجيل الدخول")
    
    # بيانات المستخدم
    cUsername = "testuser"
    cPassword = "Test@123456"
    
    # محاولة تسجيل الدخول
    oAuth = new AuthController()
    aResult = oAuth.login(cUsername, cPassword)
    
    # التحقق من نجاح تسجيل الدخول
    if aResult[:success] {
        oLogger.info("نجح تسجيل الدخول")
        assert(aResult[:user] != NULL)
        assert(aResult[:token] != NULL)
        assert(aResult[:user].getUsername() = cUsername)
    else
        oLogger.error("فشل تسجيل الدخول: " + aResult[:error])
        assert(false)
    }
}

# اختبار إدارة المفاتيح
func testKeyManagement() {
    oLogger.info("اختبار إدارة المفاتيح")
    
    # إنشاء كائن التشفير
    oCrypto = new Crypto()
    
    # توليد زوج مفاتيح
    aKeyPair = oCrypto.generateKeyPair()
    assert(aKeyPair != NULL)
    assert(aKeyPair[:public] != NULL)
    assert(aKeyPair[:private] != NULL)
    
    # اختبار تأمين المفتاح الخاص
    cPassword = "Test@123456"
    aSecuredKey = oCrypto.securePrivateKey(aKeyPair[:private], cPassword)
    assert(aSecuredKey != NULL)
    assert(aSecuredKey[:key] != NULL)
    assert(aSecuredKey[:iv] != NULL)
    assert(aSecuredKey[:salt] != NULL)
    
    # اختبار استرجاع المفتاح الخاص
    cRetrievedKey = oCrypto.retrievePrivateKey(
        aSecuredKey[:key],
        aSecuredKey[:iv],
        aSecuredKey[:salt],
        cPassword
    )
    assert(cRetrievedKey != NULL)
    assert(cRetrievedKey = aKeyPair[:private])
    
    # اختبار التوقيع والتحقق
    cTestData = "test data for signing"
    cSignature = oCrypto.sign(cTestData, aKeyPair[:private])
    assert(cSignature != NULL)
    
    bValid = oCrypto.verify(cTestData, cSignature, aKeyPair[:public])
    assert(bValid)
    
    oLogger.info("نجحت اختبارات إدارة المفاتيح")
}

# تشغيل الاختبارات
main()
