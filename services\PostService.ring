# استيراد المكتبات والنماذج اللازمة
load "../core/Service.ring"     # الخدمة الأساسية
load "../core/Database.ring"    # قاعدة البيانات
load "../models/Post.ring"      # نموذج المنشور

# خدمة المنشورات: تدير عمليات إنشاء وعرض وتفاعل المستخدمين مع المنشورات
class PostService from Service {
    # المتغيرات الخاصة بالخدمة
    private {
        oDb    # كائن قاعدة البيانات
        aPosts = []  # قائمة المنشورات
        aLikes = []  # قائمة الإعجابات
        aComments = [] # قائمة التعليقات
        aShares = []   # قائمة المشاركات
    }
    
    # دالة التهيئة: إعداد الاتصال بقاعدة البيانات
    func init() {
        oDb = new Database()
        loadData()
    }
    
    # تحميل البيانات من قاعدة البيانات
    private func loadData() {
        try {
            # تحميل المنشورات
            aResult = oDb.execute("SELECT * FROM posts ORDER BY created_at DESC")
            for oPost in aResult {
                add(aPosts, oPost)
            }
            
            # تحميل الإعجابات
            aResult = oDb.execute("SELECT * FROM likes ORDER BY created_at DESC")
            for oLike in aResult {
                add(aLikes, oLike)
            }
            
            # تحميل التعليقات
            aResult = oDb.execute("SELECT * FROM comments ORDER BY created_at DESC")
            for oComment in aResult {
                add(aComments, oComment)
            }
            
            # تحميل المشاركات
            aResult = oDb.execute("SELECT * FROM shares ORDER BY created_at DESC")
            for oShare in aResult {
                add(aShares, oShare)
            }
        catch
            handleError(cCatchError)
        }
    }
    
    # دالة إنشاء منشور جديد
    func createPost(cUserId, cContent, cMediaUrl) {
        try {
            # التحقق من وجود المستخدم
            aResult = oDb.execute("
                SELECT id FROM users WHERE id = ?
            ", [cUserId])
            
            if len(aResult) = 0 {
                return handleError("المستخدم غير موجود")
            }
            
            # إنشاء منشور جديد
            oPost = new Post(cUserId, cContent, cMediaUrl)
            aPostData = oPost.toList()
            
            # حفظ المنشور في قاعدة البيانات
            oDb.execute("
                INSERT INTO posts (
                    id, user_id, content, 
                    media_url, created_at
                ) VALUES (?, ?, ?, ?, ?)
            ", aPostData)
            
            # إضافة المنشور إلى القائمة المحلية
            add(aPosts, aPostData)
            
            return handleSuccess(aPostData)
        catch 
            return handleError(cCatchError)
        }
    }
    
    # دالة الحصول على تفاصيل منشور معين مع إحصائياته
    func getPost(cPostId) {
        try {
            # البحث عن المنشور في القائمة المحلية
            for oPost in aPosts {
                if oPost[:id] = cPostId {
                    # حساب الإحصائيات
                    nLikesCount = 0
                    nCommentsCount = 0
                    nSharesCount = 0
                    
                    for oLike in aLikes {
                        if oLike[:post_id] = cPostId {
                            nLikesCount++
                        }
                    }
                    
                    for oComment in aComments {
                        if oComment[:post_id] = cPostId {
                            nCommentsCount++
                        }
                    }
                    
                    for oShare in aShares {
                        if oShare[:post_id] = cPostId {
                            nSharesCount++
                        }
                    }
                    
                    # إضافة الإحصائيات للمنشور
                    oPost[:likes_count] = nLikesCount
                    oPost[:comments_count] = nCommentsCount
                    oPost[:shares_count] = nSharesCount
                    
                    # الحصول على معلومات المستخدم
                    aResult = oDb.execute("
                        SELECT username, avatar
                        FROM users
                        WHERE id = ?
                    ", [oPost[:user_id]])
                    
                    if len(aResult) > 0 {
                        oPost[:username] = aResult[1][:username]
                        oPost[:avatar] = aResult[1][:avatar]
                    }
                    
                    return handleSuccess(oPost)
                }
            }
            
            return handleError("المنشور غير موجود")
        catch 
            return handleError(cCatchError)
        }
    }
    
    # دالة إضافة إعجاب لمنشور
    func likePost(cUserId, cPostId) {
        try {
            # التحقق من وجود المنشور
            bFound = false
            for oPost in aPosts {
                if oPost[:id] = cPostId {
                    bFound = true
                    exit
                }
            }
            
            if not bFound {
                return handleError("المنشور غير موجود")
            }
            
            # إنشاء بيانات الإعجاب
            aLikeData = [
                :user_id = cUserId,
                :post_id = cPostId,
                :created_at = date() + " " + time()
            ]
            
            # حفظ الإعجاب في قاعدة البيانات
            oDb.execute("
                INSERT INTO likes (
                    user_id, post_id, created_at
                ) VALUES (?, ?, ?)
            ", [
                aLikeData[:user_id],
                aLikeData[:post_id],
                aLikeData[:created_at]
            ])
            
            # إضافة الإعجاب إلى القائمة المحلية
            add(aLikes, aLikeData)
            
            return handleSuccess(true)
        catch 
            return handleError(cCatchError)
        }
    }
    
    # دالة إضافة تعليق على منشور
    func addComment(cUserId, cPostId, cContent) {
        try {
            # التحقق من وجود المنشور
            bFound = false
            for oPost in aPosts {
                if oPost[:id] = cPostId {
                    bFound = true
                    exit
                }
            }
            
            if not bFound {
                return handleError("المنشور غير موجود")
            }
            
            # إنشاء بيانات التعليق
            aCommentData = [
                :user_id = cUserId,
                :post_id = cPostId,
                :content = cContent,
                :created_at = date() + " " + time()
            ]
            
            # حفظ التعليق في قاعدة البيانات
            oDb.execute("
                INSERT INTO comments (
                    user_id, post_id, content, created_at
                ) VALUES (?, ?, ?, ?)
            ", [
                aCommentData[:user_id],
                aCommentData[:post_id],
                aCommentData[:content],
                aCommentData[:created_at]
            ])
            
            # إضافة التعليق إلى القائمة المحلية
            add(aComments, aCommentData)
            
            return handleSuccess(aCommentData)
        catch 
            return handleError(cCatchError)
        }
    }
    
    # دالة مشاركة منشور
    func sharePost(cUserId, cPostId) {
        try {
            # التحقق من وجود المنشور
            bFound = false
            for oPost in aPosts {
                if oPost[:id] = cPostId {
                    bFound = true
                    exit
                }
            }
            
            if not bFound {
                return handleError("المنشور غير موجود")
            }
            
            # إنشاء بيانات المشاركة
            aShareData = [
                :user_id = cUserId,
                :post_id = cPostId,
                :created_at = date() + " " + time()
            ]
            
            # حفظ المشاركة في قاعدة البيانات
            oDb.execute("
                INSERT INTO shares (
                    user_id, post_id, created_at
                ) VALUES (?, ?, ?)
            ", [
                aShareData[:user_id],
                aShareData[:post_id],
                aShareData[:created_at]
            ])
            
            # إضافة المشاركة إلى القائمة المحلية
            add(aShares, aShareData)
            
            return handleSuccess(true)
        catch 
            return handleError(cCatchError)
        }
    }
    
    # دالة الحصول على منشورات المستخدم
    func getUserPosts(cUserId) {
        try {
            aUserPosts = []
            
            # البحث عن منشورات المستخدم في القائمة المحلية
            for oPost in aPosts {
                if oPost[:user_id] = cUserId {
                    add(aUserPosts, oPost)
                }
            }
            
            return handleSuccess(aUserPosts)
        catch 
            return handleError(cCatchError)
        }
    }
}
