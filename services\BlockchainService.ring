# استيراد المكتبات والنماذج اللازمة
load "../core/Service.ring"      # الخدمة الأساسية
load "../core/Database.ring"     # قاعدة البيانات
load "../models/Block.ring"      # نموذج الكتلة
load "../models/Transaction.ring" # نموذج المعاملة
load "../blockchain/ContractManager.ring"
load "../utils/Validator.ring"

# خدمة سلسلة الكتل: تدير العمليات المتعلقة بالمعاملات والكتل
class BlockchainService from Service {
    # المتغيرات الخاصة بالخدمة
    private {
        oDb           # كائن قاعدة البيانات
        nDifficulty   # مستوى الصعوبة للتعدين
        oContractManager = NULL
        oValidator = NULL
        aPendingTransactions = [] # المعاملات المعلقة
        aBlocks = []             # الكتل المعدنة
        aTransactionPool = []    # تجمع المعاملات
    }
    
    # دالة التهيئة: إعداد قاعدة البيانات ومستوى الصعوبة
    func init() {
        oDb = new Database()
        nDifficulty = 4  # عدد الأصفار المطلوبة في بداية الهاش
        oContractManager = new ContractManager(generateOwnerAddress())
        oValidator = new Validator()
        loadBlockchain()
    }
    
    # تحميل سلسلة الكتل من قاعدة البيانات
    private func loadBlockchain() {
        try {
            # تحميل الكتل
            aResult = oDb.execute("SELECT * FROM blocks ORDER BY created_at ASC")
            for oBlock in aResult {
                add(aBlocks, oBlock)
            }
            
            # تحميل المعاملات المعلقة
            aResult = oDb.execute("
                SELECT * FROM transactions 
                WHERE block_id IS NULL 
                ORDER BY created_at ASC
            ")
            for oTx in aResult {
                add(aPendingTransactions, oTx)
            }
        catch
            handleError(cCatchError)
        }
    }
    
    # دالة إنشاء معاملة جديدة بين مستخدمين
    func createTransaction(cSenderId, cReceiverId, nAmount) {
        try {
            # التحقق من وجود المستخدمين
            aResult = oDb.execute("
                SELECT COUNT(*) as count 
                FROM users 
                WHERE id IN (?, ?)
            ", [cSenderId, cReceiverId])
            
            if aResult[1][:count] != 2 {
                return handleError("المستخدم غير موجود")
            }
            
            # التحقق من الرصيد
            nBalance = getBalance(cSenderId)
            if nBalance < nAmount {
                return handleError("الرصيد غير كافي")
            }
            
            # إنشاء المعاملة
            oTransaction = new Transaction(cSenderId, cReceiverId, nAmount)
            
            # توقيع المعاملة باستخدام المفتاح الخاص للمرسل
            aResult = oDb.execute("
                SELECT private_key FROM users WHERE id = ?
            ", [cSenderId])
            
            cPrivateKey = aResult[1][:private_key]
            oUser = new User()
            oUser.setPrivateKey(cPrivateKey)
            
            cSignature = oUser.signMessage(oTransaction.getMessage())
            oTransaction.setSignature(cSignature)
            
            # حفظ المعاملة في قائمة الانتظار
            aTransactionData = oTransaction.toList()
            oDb.execute("
                INSERT INTO transactions (
                    id, sender_id, receiver_id,
                    amount, signature, created_at
                ) VALUES (?, ?, ?, ?, ?, ?)
            ", aTransactionData)
            
            add(aPendingTransactions, aTransactionData)
            return handleSuccess(aTransactionData)
        catch 
            return handleError(cCatchError)
        }
    }
    
    # دالة تعدين كتلة جديدة
    func mineBlock() {
        try {
            # الحصول على آخر كتلة في السلسلة
            cPreviousHash = ""
            if len(aBlocks) > 0 {
                cPreviousHash = aBlocks[len(aBlocks)][:hash]
            }
            
            # إنشاء كتلة جديدة
            oBlock = new Block(cPreviousHash)
            
            # إضافة المعاملات المعلقة إلى الكتلة
            for i = 1 to min(len(aPendingTransactions), 10) {
                oBlock.addTransaction(aPendingTransactions[i])
            }
            
            # تعدين الكتلة
            nNonce = mineBlockWithDifficulty(oBlock)
            if nNonce = 0 return handleError("فشل التعدين")
            
            oBlock.setNonce(nNonce)
            aBlockData = oBlock.toList()
            
            # حفظ الكتلة في قاعدة البيانات
            oDb.execute("
                INSERT INTO blocks (
                    hash, previous_hash, nonce,
                    difficulty, created_at
                ) VALUES (?, ?, ?, ?, ?)
            ", aBlockData)
            
            # تحديث المعاملات المضمنة في الكتلة
            for i = 1 to min(len(aPendingTransactions), 10) {
                oDb.execute("
                    UPDATE transactions 
                    SET block_id = ? 
                    WHERE id = ?
                ", [aBlockData[:id], aPendingTransactions[i][:id]])
                
                del(aPendingTransactions, 1)
            }
            
            add(aBlocks, aBlockData)
            return handleSuccess(aBlockData)
        catch
            return handleError(cCatchError)
        }
    }
    
    # تعدين كتلة مع مستوى صعوبة محدد
    private func mineBlockWithDifficulty(oBlock) {
        cTarget = substr("0", 1, nDifficulty)
        for nNonce = 1 to 1000000 {
            oBlock.setNonce(nNonce)
            cHash = oBlock.calculateHash()
            if substr(cHash, 1, nDifficulty) = cTarget {
                return nNonce
            }
        }
        return 0
    }
    
    # الحصول على رصيد المستخدم
    func getBalance(cUserId) {
        try {
            nBalance = 0
            
            # حساب المعاملات المؤكدة
            for oBlock in aBlocks {
                for oTx in oBlock[:transactions] {
                    if oTx[:sender_id] = cUserId {
                        nBalance -= oTx[:amount]
                    }
                    if oTx[:receiver_id] = cUserId {
                        nBalance += oTx[:amount]
                    }
                }
            }
            
            # حساب المعاملات المعلقة
            for oTx in aPendingTransactions {
                if oTx[:sender_id] = cUserId {
                    nBalance -= oTx[:amount]
                }
                if oTx[:receiver_id] = cUserId {
                    nBalance += oTx[:amount]
                }
            }
            
            return handleSuccess(nBalance)
        catch
            return handleError(cCatchError)
        }
    }
    
    # الحصول على معاملات المستخدم
    func getUserTransactions(cUserId) {
        try {
            aUserTransactions = []
            
            # جمع المعاملات المؤكدة
            for oBlock in aBlocks {
                for oTx in oBlock[:transactions] {
                    if oTx[:sender_id] = cUserId or oTx[:receiver_id] = cUserId {
                        add(aUserTransactions, oTx)
                    }
                }
            }
            
            # إضافة المعاملات المعلقة
            for oTx in aPendingTransactions {
                if oTx[:sender_id] = cUserId or oTx[:receiver_id] = cUserId {
                    add(aUserTransactions, oTx)
                }
            }
            
            return handleSuccess(aUserTransactions)
        catch
            return handleError(cCatchError)
        }
    }
    
    # تنفيذ عملية معقدة على العقود الذكية
    func executeComplexOperation(cOperationType, aParams) {
        return oContractManager.executeComplexOperation(cOperationType, aParams)
    }

    # توليد عنوان المالك
    private func generateOwnerAddress() {
        return "0x" + substr(sha256(string(random(1000000))), 1, 40)
    }
}
